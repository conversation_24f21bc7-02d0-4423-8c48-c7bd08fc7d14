# Luster Reminder - Implementation Plan & Progress

## Project Overview
Modern Flutter app with Admin and User sections for product reminder management using QR codes, Strapi backend, and Supabase for plan storage.

**Primary Color**: `#0C428B` (Dark Blue)
**Default Language**: Arabic
**Supported Languages**: Arabic, English

---

## Phase 0: Discovery & Setup ✅ COMPLETE

### Completed Tasks:
- ✅ Analyzed repository structure and existing patterns
- ✅ Identified xr_helper package utilities (shimmer, http, get_storage, etc.)
- ✅ Added required dependencies to pubspec.yaml:
  - supabase_flutter: ^2.8.0
  - pretty_qr: ^3.3.0
  - qr_code_dart_scan: ^0.7.1
  - image_gallery_saver_plus: ^4.0.1
  - share_plus: ^10.1.3
  - dio: ^5.7.0
  - path_provider: ^2.1.5
- ✅ Updated ColorManager with new primary color (#0C428B)
- ✅ Added primaryOpacityContainer color (10% opacity)
- ✅ Created .env.example file for Supabase credentials
- ✅ Updated .gitignore to exclude .env files
- ✅ Initialized Supabase in main.dart
- ✅ Created data models:
  - CategoryModel (Strapi structure)
  - ProductModel (Strapi structure with buyUrl)
  - PlanEntryModel (individual plan entry)
  - PlanModel (Supabase structure)
  - ScannedProductModel (local storage with progress tracking)
- ✅ Added 60+ translation keys to intl_ar.arb and intl_en.arb

### Files Created:
- `.env.example` - Environment variables template
- `.gitignore` - Updated with .env exclusions
- `lib/src/screens/products/models/category_model.dart`
- `lib/src/screens/products/models/product_model.dart`
- `lib/src/screens/products/models/plan_entry_model.dart`
- `lib/src/screens/products/models/plan_model.dart`
- `lib/src/screens/products/models/scanned_product_model.dart`

### Files Modified:
- `pubspec.yaml` - Added 7 new dependencies
- `lib/main.dart` - Added Supabase initialization
- `lib/src/core/theme/color_manager.dart` - Updated primary color
- `lib/l10n/intl_ar.arb` - Added 60+ keys
- `lib/l10n/intl_en.arb` - Added 60+ keys

### Next Steps:
1. Run `flutter pub get` to install new dependencies
2. Run `flutter pub run intl_utils:generate` to generate translation files
3. Create .env file from .env.example with actual credentials
4. Test app compilation

---

## Phase 1: UI Demos & Components (NEXT)

### Planned Tasks:

#### 1.1 Admin Screens
- [ ] Create admin product list screen with shimmer loading
- [ ] Create QR generator screen with RepaintBoundary
- [ ] Create plan creation/edit UI
- [ ] Implement product search functionality

#### 1.2 User Screens
- [ ] Create user home screen with scan FAB
- [ ] Create scanned products list with progress table
- [ ] Create product detail screen
- [ ] Create categories tabbar with product cards

#### 1.3 Reusable Components
- [ ] QR card widget (PrettyQrView + RepaintBoundary)
- [ ] Product card widget (with shimmer placeholder)
- [ ] Plan entry list widget
- [ ] Progress table widget (daily steps with current day highlight)
- [ ] Category circle avatar widget
- [ ] Shimmer placeholder widgets

#### 1.4 Styling
- [ ] Use ColorManager.primaryColor (#0C428B) for buttons, FAB, accents
- [ ] Use ColorManager.primaryOpacityContainer for card backgrounds
- [ ] Use AppSpaces, AppGaps, AppRadius from xr_helper
- [ ] Implement soft shadows and gradients
- [ ] Add smooth transitions and micro-interactions

---

## Phase 2: API Integration & Supabase

### Planned Tasks:

#### 2.1 Strapi Integration
- [ ] Add Strapi endpoints to ApiEndpoints
- [ ] Create ProductsRepository with BaseRepository pattern
- [ ] Create ProductsController with BaseVM pattern
- [ ] Create Riverpod providers
- [ ] Implement caching strategy

#### 2.2 Supabase Setup
- [ ] Create product_plans table in Supabase
- [ ] Create PlansRepository
- [ ] Implement CRUD operations
- [ ] Add error handling

#### 2.3 Admin Flows
- [ ] Connect product list to Strapi API
- [ ] Implement plan creation and save to Supabase
- [ ] Implement QR generation (PrettyQr + RepaintBoundary)
- [ ] Implement save to gallery (image_gallery_saver_plus)
- [ ] Implement share QR (share_plus)

#### 2.4 User Flows
- [ ] Implement QR scanner (qr_code_dart_scan)
- [ ] Handle camera permissions
- [ ] Fetch plan from Supabase after scan
- [ ] Save scanned product locally (GetStorage)
- [ ] Implement "Buy" button navigation
- [ ] Filter products by category

---

## Phase 3: Notifications & Polish

### Planned Tasks:

#### 3.1 Local Notifications
- [ ] Schedule notifications for each plan entry
- [ ] Handle timezone correctly
- [ ] Implement notification tap handling
- [ ] Add reschedule functionality
- [ ] Add cancel functionality

#### 3.2 Progress Tracking
- [ ] Calculate current day from scannedAt
- [ ] Implement manual completion marking
- [ ] Update progress UI in real-time
- [ ] Persist progress locally

#### 3.3 Permissions & Error Handling
- [ ] Camera permission flow (request, denied, permanently denied)
- [ ] Storage permission flow (Android API levels)
- [ ] Handle network errors gracefully
- [ ] Add retry mechanisms

#### 3.4 QA & Testing
- [ ] Test on Android (API 29+, 33+)
- [ ] Test on iOS simulator and device
- [ ] Test camera/gallery/notifications
- [ ] Test language switching
- [ ] Performance optimization

---

## Phase 4: Documentation & Handoff

### Planned Tasks:

#### 4.1 Documentation
- [ ] README section for env vars
- [ ] Supabase table structure documentation
- [ ] Migration steps for production
- [ ] API integration guide

#### 4.2 Permissions Documentation
- [ ] Android permissions (English + Arabic)
- [ ] iOS Info.plist strings (English + Arabic)
- [ ] Permission request flow documentation

#### 4.3 Testing & Handoff
- [ ] Create test plan document
- [ ] Provide sample test data
- [ ] Create demo video/screenshots
- [ ] Handoff checklist

---

## Supabase Table Schema

### product_plans Table

```sql
CREATE TABLE product_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_document_id TEXT NOT NULL,
  plan_index INTEGER NOT NULL DEFAULT 1,
  entries JSONB NOT NULL,
  created_by_admin TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for faster lookups
CREATE INDEX idx_product_plans_document_id ON product_plans(product_document_id);

-- Example entries JSONB structure:
-- [
--   {"day": 1, "time": "09:00", "key": "Shampoo", "value": "Clean hair", "repeatCount": 1},
--   {"day": 1, "time": "21:00", "key": "Conditioner", "value": "Moisturize", "repeatCount": 1},
--   {"day": 2, "time": "09:00", "key": "Shampoo", "value": "Clean hair", "repeatCount": 1}
-- ]
```

---

## API Endpoints

### Strapi (Products & Categories)

**Base URL**: `https://backend.idea2app.tech/api`

**Categories**:
```
GET /product-categories?filters[vendor][business_name]=luster&sort[0]=sort:asc&populate=3
```

**Products**:
```
GET /products?filters[vendor][business_name]=luster&sort[0]=sort:asc&populate=2
```

### Supabase (Plans)

**Base URL**: `https://wfrofrmeibpidjujrzrv.supabase.co`

**Get Plan**:
```
GET /rest/v1/product_plans?product_document_id=eq.{documentId}
```

**Create Plan**:
```
POST /rest/v1/product_plans
Body: { product_document_id, plan_index, entries, created_by_admin }
```

**Update Plan**:
```
PATCH /rest/v1/product_plans?id=eq.{id}
Body: { entries, updated_at }
```

---

## Development Workflow

### Setup
1. Clone repository
2. Run `flutter pub get`
3. Run `flutter pub run intl_utils:generate`
4. Copy `.env.example` to `.env` and fill in credentials
5. Run app: `flutter run`

### Build
- Android: `flutter build apk --release`
- iOS: `flutter build ios --release`

### Code Generation
- Translations: `flutter pub run intl_utils:generate`
- Assets: `flutter packages pub run build_runner build --delete-conflicting-outputs`

---

## Notes

- **Security**: Never commit .env file or Supabase keys to version control
- **Testing**: Test on both Android and iOS before each PR
- **Translations**: Always add keys to both intl_ar.arb and intl_en.arb
- **Styling**: Use existing ColorManager, AppSpaces, AppGaps, AppRadius
- **Patterns**: Follow existing BaseRepository, BaseVM, Riverpod patterns

