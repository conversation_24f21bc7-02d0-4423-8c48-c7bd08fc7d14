# 🔄 ProductModel Update - Arabic/English Support & Sale Prices

**Date:** January 2025  
**Status:** ✅ **Complete**

---

## 📋 Summary

The `ProductModel` has been completely refactored to support:
1. **Bilingual Support** - Separate fields for Arabic and English titles/descriptions
2. **Sale Pricing** - Support for sale prices with `isSale` flag
3. **Language-Aware Methods** - `nameByLang()` and `descriptionByLang()` methods
4. **Actual Price Calculation** - `actualPrice` getter that returns sale price when on sale

---

## ✅ What Changed

### 1. **New Fields Added**

| Old Field | New Fields | Type |
|-----------|------------|------|
| `name` | `englishTitle`, `arabicTitle` | `String?` |
| `description` | `englishDescription`, `arabicDescription` | `String?` |
| `price` | `totalPrice`, `salePrice`, `isSale` | `num?`, `num?`, `bool` |
| `image` | `thumbnail` | `String?` |
| `documentId` | `documentId` | `String?` (now nullable) |

### 2. **New Methods & Getters**

#### `nameByLang(BuildContext context)`
Returns the product name in the current language:
```dart
String nameByLang(BuildContext context) {
  final isEng = context.isEnglish;
  return isEng
      ? (englishTitle ?? arabicTitle ?? '')
      : (arabicTitle ?? englishTitle ?? '');
}
```

#### `descriptionByLang(BuildContext context)`
Returns the product description in the current language:
```dart
String? descriptionByLang(BuildContext context) {
  final isEng = context.isEnglish;
  return isEng
      ? (englishDescription ?? arabicDescription)
      : (arabicDescription ?? englishDescription);
}
```

#### `actualPrice` Getter
Returns the sale price if on sale, otherwise returns total price:
```dart
num get actualPrice => isSale ? (salePrice ?? 0) : (totalPrice ?? 0);
```

---

## 🔄 JSON Mapping Changes

### API Field Mapping:

| API Field | ProductModel Field |
|-----------|-------------------|
| `title` | `englishTitle` |
| `title_ar` | `arabicTitle` |
| `description` | `englishDescription` |
| `description_ar` | `arabicDescription` |
| `price` | `totalPrice` |
| `sale_price` | `salePrice` |
| `is_sale` | `isSale` |
| `images` | `images` (List<String>) |
| `categories` | `category` (CategoryModel) |

### Example JSON:
```json
{
  "id": 1,
  "documentId": "abc123",
  "title": "Luster Cream",
  "title_ar": "كريم لستر",
  "description": "Premium skin care cream",
  "description_ar": "كريم عناية بالبشرة فاخر",
  "price": 100,
  "sale_price": 80,
  "is_sale": true,
  "images": [
    {"url": "/uploads/image1.jpg"},
    {"url": "/uploads/image2.jpg"}
  ],
  "categories": [
    {"documentId": "cat1", "name": "Skin Care"}
  ]
}
```

---

## 📝 Updated Files

### Models:
1. ✅ `lib/src/screens/products/models/product_model.dart` - Complete refactor

### Widgets:
2. ✅ `lib/src/screens/products/widgets/product_card.widget.dart`
   - Changed `product.name` → `product.nameByLang(context)`
   - Changed `product.description` → `product.descriptionByLang(context)`
   - Changed `product.price` → `product.actualPrice`
   - Added sale price display with strikethrough for original price

### Screens:
3. ✅ `lib/src/screens/products/view/admin/qr_generator_screen.dart`
   - Updated to use `nameByLang()` and `descriptionByLang()`
   - Fixed nullable `documentId` handling

4. ✅ `lib/src/screens/products/view/user/scanned_product_detail_screen.dart`
   - Updated to use `nameByLang()` and `descriptionByLang()`

5. ✅ `lib/src/screens/products/view/user/user_home_screen.dart`
   - Updated to use `nameByLang()`

---

## 🎨 UI Changes

### Product Card - Before:
```dart
Text(product.name)  // Always English
Text('${product.price} EGP')  // No sale price support
```

### Product Card - After:
```dart
Text(product.nameByLang(context))  // Arabic or English based on locale

// Sale price with strikethrough
if (product.isSale && product.totalPrice != null) {
  Text(
    '${product.totalPrice} EGP',
    style: TextStyle(decoration: TextDecoration.lineThrough),
  ),
}
Text(
  '${product.actualPrice} EGP',
  style: TextStyle(
    color: product.isSale ? Colors.red : Colors.primary,
  ),
)
```

---

## 🌍 Language Support

### English Display:
- Shows `englishTitle` if available
- Falls back to `arabicTitle` if English not available
- Same logic for descriptions

### Arabic Display:
- Shows `arabicTitle` if available
- Falls back to `englishTitle` if Arabic not available
- Same logic for descriptions

### Example:
```dart
// English locale
product.nameByLang(context) // Returns "Luster Cream"

// Arabic locale  
product.nameByLang(context) // Returns "كريم لستر"
```

---

## 💰 Sale Price Display

### Regular Price:
```dart
product.isSale = false
product.totalPrice = 100
product.actualPrice // Returns 100
```

### Sale Price:
```dart
product.isSale = true
product.totalPrice = 100
product.salePrice = 80
product.actualPrice // Returns 80
```

### UI Display:
- Regular price: Shows `actualPrice` in primary color
- Sale price: Shows `totalPrice` with strikethrough + `actualPrice` in red

---

## ✅ Verification

### Analysis Results:
```bash
flutter analyze
```
- ✅ **0 errors**
- ✅ **0 warnings** (related to this change)

### Type Safety:
- ✅ All fields properly typed
- ✅ Nullable fields handled correctly
- ✅ BuildContext properly used for language detection

---

## 🔍 Breaking Changes

### Old Code (Will Break):
```dart
❌ product.name
❌ product.description
❌ product.price
❌ product.image
```

### New Code (Correct):
```dart
✅ product.nameByLang(context)
✅ product.descriptionByLang(context)
✅ product.actualPrice
✅ product.primaryImage
```

---

## 📚 Migration Guide

If you have custom code using ProductModel:

### 1. Replace Direct Field Access:
```dart
// Before
Text(product.name)

// After
Text(product.nameByLang(context))
```

### 2. Update Price Display:
```dart
// Before
Text('${product.price} EGP')

// After
Text('${product.actualPrice} EGP')
```

### 3. Handle Sale Prices:
```dart
// Show both prices when on sale
if (product.isSale) {
  Text('${product.totalPrice} EGP', 
    style: TextStyle(decoration: TextDecoration.lineThrough));
  Text('${product.actualPrice} EGP', 
    style: TextStyle(color: Colors.red));
} else {
  Text('${product.actualPrice} EGP');
}
```

---

## 🎯 Benefits

1. **✅ Full Bilingual Support** - Seamless Arabic/English switching
2. **✅ Sale Price Management** - Built-in sale price logic
3. **✅ Type Safety** - Proper nullable handling
4. **✅ Consistent API** - All screens use same methods
5. **✅ Fallback Logic** - Graceful degradation when translations missing
6. **✅ Better UX** - Shows sale prices with visual distinction

---

## 🚀 Next Steps

The ProductModel is now production-ready with:
- ✅ Complete bilingual support
- ✅ Sale price functionality
- ✅ All screens updated
- ✅ Zero compilation errors
- ✅ Type-safe implementation

**No further changes needed!** 🎉

---

## 📖 API Contract

### Required API Fields:
- `documentId` - Product identifier
- `title` - English title
- `title_ar` - Arabic title

### Optional API Fields:
- `description` - English description
- `description_ar` - Arabic description
- `price` - Regular price
- `sale_price` - Sale price
- `is_sale` - Sale flag
- `images` - Array of image objects
- `categories` - Array of category objects

---

## ✨ Example Usage

```dart
// In any widget with BuildContext
final product = ProductModel(...);

// Get localized name
final name = product.nameByLang(context);

// Get localized description
final desc = product.descriptionByLang(context);

// Get actual price (considers sale)
final price = product.actualPrice;

// Check if on sale
if (product.isSale) {
  print('Save ${product.totalPrice! - product.salePrice!} EGP!');
}

// Get primary image
final imageUrl = product.primaryImage;

// Get buy URL
final url = product.buyUrl;
```

---

**All ProductModel updates complete!** ✅

