import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/auth_controller.dart';
import '../repositories/auth_repository.dart';

// * Auth Repo Provider ========================================
final authRepoProvider = Provider<AuthRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);
  final supabase = Supabase.instance.client;

  return AuthRepository(
    networkApiService: networkApiService,
    supabase: supabase,
  );
});

// * Auth Change Notifier Provider ========================================
final authControllerNotifierProvider = ChangeNotifierProvider<AuthController>(
  (ref) {
    final authRepo = ref.watch(authRepoProvider);

    return AuthController(
      authRepo: authRepo,
    );
  },
);

// * Auth Provider ========================================
final authControllerProvider = Provider<AuthController>(
  (ref) {
    final authRepo = ref.watch(authRepoProvider);

    return AuthController(
      authRepo: authRepo,
    );
  },
);

// * Get Countries Future Provider ========================================
// final getCountriesFutureProvider = FutureProvider(
//   (
//     ref,
//   ) {
//     final authController = ref.watch(authControllerProvider);
//
//     return authController.getCountries();
//   },
// );
