import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:xr_helper/xr_helper.dart';

class UserModel extends Equatable {
  final String? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? password;
  final String? fcmToken;
  final String role; //admin, user

  const UserModel({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.password,
    this.fcmToken,
    this.role = 'user',
  });

  // * For Login ================================
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'] ?? '',
      email: json['email'],
      phone: json['phone'] ?? '',
      fcmToken: json['fcm_token'] ?? '',
      role: json['role'] ?? 'user',
    );
  }

  // * To JSON for storage
  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      if (fcmToken != null && fcmToken!.isNotEmpty) 'fcm_token': fcmToken,
      'role': role,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        password,
        fcmToken,
      ];

  // empty
  static bool get isAdmin =>
      currentUser().name?.toLowerCase() == 'admin' ||
      currentUser().role == 'admin';

  //? Get saved user
  static UserModel currentUser() {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    if (userData == null) {
      return const UserModel();
    } else {
      return UserModel.fromJson(userData);
    }
  }
}
