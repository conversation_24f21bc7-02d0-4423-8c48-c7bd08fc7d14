import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:xr_helper/xr_helper.dart';

class UserModel extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? password;
  final String? fcmToken;
  final String role; //admin, user

  const UserModel({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.password,
    this.fcmToken,
    this.role = 'user',
  });

  // * For Login ================================
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'] ?? '',
      email: json['email'],
      phone: json['phone'] ?? '',
      fcmToken: json['fcm_token'] ?? '',
      role: json['role'] ?? 'user',
    );
  }

  //? Copy With

  // * To Register Json ================================
  Map<String, dynamic> toRegisterJson() {
    return {
      'name': name,
      'phone': phone,
      'password': password,
      if (fcmToken != null && fcmToken!.isNotEmpty) 'fcm_token': fcmToken,
      'role': role,
    };
  }

  // * To Login Json ================================
  Map<String, dynamic> toLoginJson() {
    return {
      'phone': phone,
      'password': password,
      if (fcmToken != null && fcmToken!.isNotEmpty) 'fcm_token': fcmToken,
    };
  }

  // * To Verify Json ================================
  Map<String, dynamic> toVerifyJson(String code) {
    return {
      'phone': phone,
      'code': code,
    };
  }

  // * To JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      if (fcmToken != null && fcmToken!.isNotEmpty) 'fcm_token': fcmToken,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        password,
        fcmToken,
      ];

  // empty
  static bool get isAdmin => kDebugMode
      ? true
      : currentUser().id != null || currentUser().role == 'admin';

  //? Get saved user
  static UserModel currentUser() {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    if (userData == null) {
      return const UserModel();
    } else {
      return UserModel.fromJson(userData);
    }
  }
}
