import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/consts/network/api_strings.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/fields/text_field.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/auth/providers/auth_providers.dart';
import 'package:luster/src/screens/auth/view/login/widgets/login_buttons.widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart'
    show bottomNavControllerProvider;

class LoginFieldsContainer extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const LoginFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final isFormValid = useState(false);

    // Form validation state
    final phoneValid = useState(false);
    final passwordValid = useState(false);

    // Update form validity
    useEffect(() {
      isFormValid.value = phoneValid.value && passwordValid.value;
      return null;
    }, [phoneValid.value, passwordValid.value]);

    void login() async {
      // Trigger validation to show errors on all fields
      if (!formKey.currentState!.validate()) return;

      // Save the form data
      formKey.currentState!.save();

      final data = formKey.currentState?.instantValue ?? {};

      await authController.login(data: data);

      ref.read(bottomNavControllerProvider).changeIndex(0);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        children: [
          // Phone Number Field
          BaseTextField(
            name: FieldsConsts.mobile,
            initialValue: kDebugMode ? '**********' : '',
            title: context.tr.mobileNumber,
            hint: context.tr.phoneHint,
            textInputType: TextInputType.phone,
            useUnderlineBorder: true,
            validator: (value) => Validations.palestinianPhoneNumber(
              value,
              emptyMessage: context.tr.mobileNumber,
              invalidMessage: context.tr.invalidPhoneNumber,
            ),
            realTimeValidator: (value) {
              final error = Validations.palestinianPhoneNumber(
                value,
                emptyMessage: context.tr.mobileNumber,
                invalidMessage: context.tr.invalidPhoneNumber,
              );
              phoneValid.value = error == null;
              return error;
            },
          ),

          AppGaps.gap16,

          // Password Field
          BaseTextField(
            name: FieldsConsts.password,
            initialValue: kDebugMode ? '12345678' : '',
            title: context.tr.password,
            hint: context.tr.password,
            textInputType: TextInputType.visiblePassword,
            isObscure: true,
            useUnderlineBorder: true,
            validator: (value) => Validations.password(
              value,
              emptyPasswordMessage: context.tr.password,
            ),
            realTimeValidator: (value) {
              final error = Validations.password(
                value,
                emptyPasswordMessage: context.tr.password,
              );
              passwordValid.value = error == null;
              return error;
            },
          ),

          SizedBox(height: context.height * 0.12),

          // Buttons
          LoginButtons(
            isLoading: authController.isLoading,
            isFormValid: isFormValid.value,
            onLogin: login,
          ),
          AppGaps.gap48,
        ],
      ),
    );
  }
}
