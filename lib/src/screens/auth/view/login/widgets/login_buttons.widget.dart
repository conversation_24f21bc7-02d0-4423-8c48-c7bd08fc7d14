import 'package:flutter/material.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/loading/loading_widget.dart';

class LoginButtons extends StatelessWidget {
  final bool isLoading;
  final bool isFormValid;
  final VoidCallback onLogin;

  const LoginButtons({
    super.key,
    required this.isLoading,
    required this.isFormValid,
    required this.onLogin,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Login Button
        SizedBox(
          height: 50,
          width: double.infinity,
          child: But<PERSON>(
            onPressed: !isLoading ? onLogin : null,
            isLoading: isLoading,
            loadingWidget: LoadingWidget(),
            label: context.tr.login,
          ),
        ),

        AppGaps.gap16,

        // Don't Have Account Text
        Text(
          context.tr.dontHaveAccount,
          textAlign: TextAlign.center,
          style: AppTextStyles.labelMedium.copyWith(
            color: Colors.black54,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
