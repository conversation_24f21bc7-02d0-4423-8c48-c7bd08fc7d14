import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../generated/assets.gen.dart';
import '../../../../core/shared/widgets/app_bar/base_header.widget.dart';
import 'widgets/login_fields_container.widget.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: FormBuilder(
          key: form<PERSON><PERSON>,
          child: SingleChildScrollView(
            child: <PERSON><PERSON><PERSON>(
              child: Column(
                children: [
                  Assets.images.logoSymbol.image(fit: BoxFit.cover, width: 200),
                  AppGaps.gap12,
                  <PERSON>gin<PERSON>ieldsContainer(
                    form<PERSON>ey: form<PERSON>ey,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
