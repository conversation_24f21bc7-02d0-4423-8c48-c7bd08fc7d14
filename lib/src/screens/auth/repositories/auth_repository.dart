import 'package:luster/src/core/consts/network/api_endpoints.dart';
import 'package:luster/src/screens/auth/models/user_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthRepository with BaseRepository {
  final BaseApiServices networkApiService;
  final SupabaseClient supabase;

  AuthRepository({
    required this.networkApiService,
    required this.supabase,
  });

  // * Login with Supabase
  Future<bool> login({
    required String email,
    required String password,
  }) async {
    return await baseFunction(
      () async {
        // Authenticate with Supabase
        final response = await supabase.auth.signInWithPassword(
          email: email,
          password: password,
        );

        // Get user data from Supabase auth
        final authUser = response.user;
        if (authUser == null) {
          throw Exception('Login failed: No user returned');
        }

        // Fetch user profile from database
        final userProfile = await supabase
            .from('users')
            .select()
            .eq('id', authUser.id)
            .single();

        // Create UserModel from Supabase response
        final user = UserModel(
          id: authUser.id.hashCode, // Convert UUID to int
          name: userProfile['name'] ?? '',
          email: authUser.email,
          phone: userProfile['phone'] ?? '',
        );

        // Save user data locally
        saveUserData(user);

        return true;
      },
    );
  }

  // * Register
  Future<Map<String, dynamic>> register({
    required UserModel user,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.register;

        final response = await networkApiService.postResponse(
          url,
          body: user.toRegisterJson(),
        );

        return response['data'];
      },
    );
  }

  // * Verify Code
  Future<bool> verifyCode({
    required UserModel user,
    required String code,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.verifyCode;

        final response = await networkApiService.postResponse(url,
            body: user.toVerifyJson(code));

        saveUserData(response);

        return true;
      },
    );
  }

  // * Resend Code
  Future<void> resendCode({
    required String phone,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.resendCode;

        await networkApiService.postResponse(url, body: {'phone': phone});
      },
    );
  }

  // * Forgot Password
  Future<bool> forgotPassword({
    required String phone,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.forgotPassword;

        await networkApiService.postResponse(url, body: {'phone': phone});

        return true;
      },
    );
  }

  // * Verify Reset Code
  Future<bool> verifyResetCode({
    required String phone,
    required String code,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.verifyResetCode;

        await networkApiService.postResponse(url, body: {
          'phone': phone,
          'verification_code': code,
        });

        return true;
      },
    );
  }

  // * Reset Password
  Future<bool> resetPassword({
    required String phone,
    required String password,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.resetPassword;

        final response = await networkApiService.postResponse(url, body: {
          'phone': phone,
          'password': password,
        });

        saveUserData(response);

        return true;
      },
    );
  }

  // * Update Profile
  Future<bool> updateProfile({
    required Map<String, dynamic> data,
    String? filePath,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.profile;

        final response = await networkApiService.postResponse(
          url,
          body: data,
          filePaths: filePath != null ? [filePath] : [],
        );

        // Only save user data if phone wasn't changed (no verification needed)
        final currentUser = UserModel.currentUser();
        final newPhone = data['phone'];

        if (newPhone == null || newPhone == currentUser.phone) {
          saveUserData(response);
        }

        return true;
      },
    );
  }

  // * Update Local User Data
  void updateLocalUserData(Map<String, dynamic> data) {
    final currentUser = UserModel.currentUser();

    final updatedUser = UserModel(
      id: currentUser.id,
      name: data['name'] ?? currentUser.name,
      email: data['email'] ?? currentUser.email,
      phone: data['phone'] ?? currentUser.phone,
    );

    GetStorageService.setData(
      key: LocalKeys.user,
      value: updatedUser.toJson(),
    );
  }

  // * Save to local
  void saveUserData(UserModel user) {
    GetStorageService.setData(
      key: LocalKeys.user,
      value: user.toJson(),
    );

    // Save Supabase session token if available
    final session = supabase.auth.currentSession;
    if (session?.accessToken != null) {
      GetStorageService.setData(
        key: LocalKeys.token,
        value: session!.accessToken,
      );
    }
  }

  // * Get Countries
  // Future<List<CountryModel>> getCountries() async {
  //   return baseFunction(
  //     () async {
  //       const url = ApiEndpoints.countries;
  //
  //       final response = await networkApiService.getResponse(url);
  //
  //       final countries = response['countries'] as List;
  //
  //       final countriesList =
  //           countries.map((country) => CountryModel.fromJson(country)).toList();
  //
  //       return countriesList;
  //     },
  //   );
  // }

  // * Logout
  Future<void> logout() async {
    await baseFunction(
      () async {
        // Sign out from Supabase
        await supabase.auth.signOut();

        //! Clear Local Data
        GetStorageService.clearLocalData();
      },
    );
  }
}
