import 'package:luster/src/core/consts/network/api_strings.dart';
import 'package:luster/src/screens/auth/models/user_model.dart';
import 'package:luster/src/screens/auth/repositories/auth_repository.dart';
import 'package:luster/src/screens/auth/view/login/login.screen.dart';
import 'package:luster/src/screens/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthController extends BaseVM {
  final AuthRepository authRepo;

  AuthController({
    required this.authRepo,
  });

  // * Login with Supabase
  Future<bool> login({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        // Extract email and password from form data
        // Phone is used as email in Supabase
        final email = data[FieldsConsts.email] as String;
        final password = data[FieldsConsts.password] as String;

        // Convert phone to email format for Supabase
        // Supabase requires email format, so <NAME_EMAIL>

        final userData = await authRepo.login(
          email: email,
          password: password,
        );

        return userData;
      },
      additionalFunction: () {
        const MainScreen(
          homeCurrentIndex: 0,
        ).navigateReplacement;
      },
    );
  }

  // * Update Profile
  Future<bool> updateProfile({
    required Map<String, dynamic> data,
    String? filePath,
  }) async {
    return await baseFunction(() async {
      final result =
          await authRepo.updateProfile(data: data, filePath: filePath);
      return result;
    }, additionalFunction: () {
      // Update local data and show success
      authRepo.updateLocalUserData(data);
      showToast('تم تحديث الملف الشخصي بنجاح');
    });
  }

  // * Set Login User
  Future<UserModel> _setLoginUser(
    Map<String, dynamic> data,
  ) async {
    final deviceToken = await NotificationService.getToken();

    final user = UserModel(
      phone: data[FieldsConsts.mobile],
      password: data[FieldsConsts.password],
      fcmToken: deviceToken,
    );

    return user;
  }

  // * Set Register User
  Future<UserModel> _setRegisterUser(
    Map<String, dynamic> data,
  ) async {
    final deviceToken = await NotificationService.getToken();

    final user = UserModel(
      name: data[FieldsConsts.name],
      phone: data[FieldsConsts.mobile],
      password: data[FieldsConsts.password],
      fcmToken: deviceToken,
    );

    return user;
  }

  // * Logout
  Future<void> logout() async {
    return await baseFunction(
      () async {
        await authRepo.logout();

        const LoginScreen().navigateReplacement;
      },
    );
  }

// * Get Countries
// Future<List<CountryModel>> getCountries() async {
//   return await baseFunction(
//     () async {
//       return await authRepo.getCountries();
//     },
//   );
// }
}
