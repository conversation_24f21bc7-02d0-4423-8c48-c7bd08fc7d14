import 'package:luster/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/lists/base_list.dart';
import 'package:luster/src/core/shared/widgets/tabs/custom_tab_bar.widget.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/models/user_model.dart';
import '../../notifications/view/notifications.screen.dart';

class HomeScreen extends HookConsumerWidget {
  final int? homeCurrentIndex;

  const HomeScreen({super.key, this.homeCurrentIndex});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        centerTitle: false,
        title: Text(
          UserModel.currentUser().name ?? '',
          style: AppTextStyles.title,
        ),
        leading: Padding(
          padding: const EdgeInsets.only(
            right: AppSpaces.padding8,
            bottom: AppSpaces.padding8,
            top: AppSpaces.padding8,
          ),
          child: CircleAvatar(
            backgroundColor: ColorManager.lightPrimaryColor,
            radius: 40.r,
            child: ClipOval(
              child:  BaseCachedImage(
                      height: 80.h,
                      'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                      fit: BoxFit.cover,
                    ),
            ),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              const NotificationsScreen().navigate;
            },
            icon: const Icon(Icons.notifications),
          ),
        ],
      ),
      body: SizedBox(),
    );
  }
}
