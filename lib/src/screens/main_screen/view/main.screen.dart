import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/home/<USER>/home.screen.dart';
import 'package:luster/src/screens/profile/view/profile.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../generated/assets.gen.dart';
import '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';

class MainScreen extends HookConsumerWidget {
  final int? homeCurrentIndex;

  const MainScreen({
    super.key,
    this.homeCurrentIndex,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);

    return WillPopScope(
      onWillPop: () async {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(context.tr.exitApp),
            content: Text(context.tr.exitAppConfirmation),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(context.tr.cancel),
              ),
              TextButton(
                onPressed: () => SystemNavigator.pop(),
                child: Text(
                  context.tr.exit,
                  style: TextStyle(color: Colors.red),
                ),
              ),
            ],
          ),
        );
        return false;
      },
      child: Scaffold(
        body: _SelectedScreen(
            currentIndex: currentIndex, homeCurrentIndex: homeCurrentIndex),
        bottomNavigationBar: const BottomNavBarWidget(),
      ),
    );
  }
}

String selectedTitle(int currentIndex, BuildContext context) {
  switch (currentIndex) {
    case 0:
      return context.tr.homePage;

    case 0:
      return context.tr.tasks;

    case 2:
      return context.tr.profile;
  }

  return context.tr.homePage;
}

class _SelectedScreen extends StatelessWidget {
  final int currentIndex;
  final int? homeCurrentIndex;

  const _SelectedScreen({
    required this.currentIndex,
    this.homeCurrentIndex,
  });

  @override
  Widget build(BuildContext context) {
    switch (currentIndex) {
      case 0:
        return HomeScreen(
          homeCurrentIndex: homeCurrentIndex,
        );
      case 2:
        return const ProfileScreen();
    }
    return const SizedBox.shrink();
  }
}
