import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/notifications_controller.dart';
import '../repositories/notifications_repository.dart';

// * Notifications Repo Provider ========================================
final notificationsRepoProvider = Provider<NotificationsRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return NotificationsRepository(networkApiService: networkApiService);
});

// * Notifications Controller Provider ========================================
final notificationsControllerProvider = Provider<NotificationsController>(
  (ref) {
    final notificationsRepo = ref.watch(notificationsRepoProvider);

    return NotificationsController(
      notificationsRepo: notificationsRepo,
    );
  },
);

// * Get Notifications Future Provider ========================================
final getNotificationsFutureProvider = FutureProvider(
  (ref) {
    final notificationsController = ref.watch(notificationsControllerProvider);

    return notificationsController.getNotifications();
  },
);

