import 'package:luster/src/screens/notifications/view/widgets/notification_tile.widget.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:luster/src/core/shared/widgets/lists/base_list.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/notifications/models/notification.model.dart';
import 'package:luster/src/screens/notifications/providers/notifications_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class NotificationsScreen extends HookConsumerWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsAsync = ref.watch(getNotificationsFutureProvider);

    return Scaffold(
      backgroundColor: ColorManager.white,
      appBar: AppBar(
        title: Text(
          context.tr.notifications,
          style: AppTextStyles.title,
        ),
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(getNotificationsFutureProvider);
          await ref.read(getNotificationsFutureProvider.future);
        },
        child: notificationsAsync.get<Widget>(
          data: (notificationsResponse) {
            final notifications = notificationsResponse.data.notifications;

            if (notifications.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.notifications_none,
                      size: 80.w,
                      color: ColorManager.greyIcon,
                    ),
                    AppGaps.gap16,
                    Text(
                      context.tr.noNotifications,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: ColorManager.greyText,
                      ),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                // Unread count badge
                if (notificationsResponse.data.unreadCount > 0)
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 8.h,
                    ),
                    decoration: BoxDecoration(
                      color: ColorManager.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      '${notificationsResponse.data.unreadCount} ${context.tr.unreadNotifications}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                // Notifications List
                Expanded(
                  child: BaseList<NotificationModel>(
                    data: notifications,
                    padding: EdgeInsets.only(bottom: 10.h),
                    itemBuilder: (notification, index) =>
                        NotificationTileWidget(
                      notification: notification,
                      onTap: notification.isRead
                          ? null
                          : () async {
                              // Mark notification as read
                              try {
                                await ref
                                    .read(notificationsControllerProvider)
                                    .markNotificationAsRead(
                                      notificationId: notification.id,
                                    );

                                // Refresh the notifications list
                                ref.invalidate(getNotificationsFutureProvider);
                              } catch (e) {
                                if (context.mounted) {
                                  showToast(context.tr.error, isError: true);
                                }
                              }
                            },
                    ),
                  ),
                ),
              ],
            );
          },
          error: (error, stackTrace) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error,
                    size: 80.w,
                    color: ColorManager.greyIcon,
                  ),
                  AppGaps.gap16,
                  Text(
                    context.tr.error,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: ColorManager.greyText,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
