import 'package:luster/src/core/consts/network/api_endpoints.dart';
import 'package:luster/src/screens/notifications/models/notifications_response.model.dart';
import 'package:xr_helper/xr_helper.dart';

class NotificationsRepository with BaseRepository {
  final BaseApiServices networkApiService;

  NotificationsRepository({
    required this.networkApiService,
  });

  // * Get Notifications
  Future<NotificationsResponse> getNotifications() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.notifications;

        final response = await networkApiService.getResponse(url);

        return NotificationsResponse.fromJson(response);
      },
    );
  }

  // * Mark Notification as Read
  Future<bool> markNotificationAsRead({required String notificationId}) async {
    return baseFunction(
      () async {
        final url = '${ApiEndpoints.notifications}/$notificationId/read';

        await networkApiService.postResponse(url, body: {});

        return true;
      },
    );
  }
}
