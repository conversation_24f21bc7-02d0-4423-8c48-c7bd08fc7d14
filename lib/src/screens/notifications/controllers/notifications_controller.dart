import 'package:luster/src/screens/notifications/models/notifications_response.model.dart';
import 'package:luster/src/screens/notifications/repositories/notifications_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class NotificationsController extends BaseVM {
  final NotificationsRepository notificationsRepo;

  NotificationsController({
    required this.notificationsRepo,
  });

  // * Get Notifications
  Future<NotificationsResponse> getNotifications() async {
    return await baseFunction(
      () async {
        return await notificationsRepo.getNotifications();
      },
    );
  }

  // * Mark Notification as Read
  Future<bool> markNotificationAsRead({required String notificationId}) async {
    return await baseFunction(
      () async {
        return await notificationsRepo.markNotificationAsRead(
          notificationId: notificationId,
        );
      },
    );
  }
}
