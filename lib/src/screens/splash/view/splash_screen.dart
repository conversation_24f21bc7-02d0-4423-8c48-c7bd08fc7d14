import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:luster/generated/assets.gen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/view/login/login.screen.dart';

import '../../main_screen/view/main.screen.dart';
import '../../products/view/user/home/<USER>';

class SplashScreen extends HookWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loggedIn = GetStorageService.hasData(key: LocalKeys.user);

    useEffect(() {
      final navigateWidget =
          // UserHomeScreen();
          LoginScreen();
      // loggedIn ? const MainScreen() : const LoginScreen();
      // RegisterScreen();
      // OnBoardingScreen();
      // const LoginScreen();

      Future.delayed(const Duration(seconds: 2), () {
        navigateWidget.navigateReplacement;
      });

      return () {};
    }, []);

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.padding24),
        child: Center(
          child: Assets.images.logoSymbol.image(fit: BoxFit.cover, width: 200),
        ),
      ),
    );
  }
}
