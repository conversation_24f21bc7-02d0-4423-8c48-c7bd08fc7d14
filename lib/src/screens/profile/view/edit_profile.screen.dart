import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/consts/network/api_strings.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/app_bar/base_header.widget.dart';
import 'package:luster/src/core/shared/widgets/fields/text_field.dart';
import 'package:luster/src/core/shared/widgets/fields/base_image_picker.dart';
import 'package:luster/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:luster/src/screens/auth/models/user_model.dart';
import 'package:luster/src/screens/auth/providers/auth_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class EditProfileScreen extends HookConsumerWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();

    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());
    final currentUser = UserModel.currentUser();

    final isFormValid = useState(false);
    final nameValid = useState(true);
    final phoneValid = useState(true);
    final passwordValid = useState(true);
    final confirmPasswordValid = useState(true);
    final selectedImagePath = useState<String?>(null);

    // Update form validity
    useEffect(() {
      isFormValid.value = nameValid.value &&
          phoneValid.value &&
          passwordValid.value &&
          confirmPasswordValid.value;
      return null;
    }, [
      nameValid.value,
      phoneValid.value,
      passwordValid.value,
      confirmPasswordValid.value
    ]);

    void saveProfile() {
      if (passwordController.text.isNotEmpty &&
          confirmPasswordController.text.isNotEmpty &&
          passwordController.text != confirmPasswordController.text) {
        showToast(context.tr.passwordsDoNotMatch, isError: true);
        return;
      }

      if (!formKey.currentState!.saveAndValidate()) return;

      log('asfasf ${GetStorageService.getData(key: LocalKeys.token)}');
      final data = formKey.currentState?.instantValue ?? {};
      final newMap = Map<String, dynamic>.from(data);

      // Remove empty password fields
      if (passwordController.text.isEmpty) {
        newMap.remove(FieldsConsts.password);
        newMap.remove(FieldsConsts.confirmPassword);
      }

      // Remove profile_photo from data as it's handled separately
      newMap.remove(FieldsConsts.profilePhoto);

      authController.updateProfile(
        data: newMap,
        filePath: selectedImagePath.value,
      );
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.white,
        body: FormBuilder(
          key: formKey,
          child: BaseHeaderWidget(
            withBackButton: true,
            path: 'edit_profile',
            child: SafeArea(
                child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    AppGaps.gap24,

                    // Profile Photo Picker
                    SizedBox(
                      width: 300,
                      child: BaseImagePicker(
                        name: FieldsConsts.profilePhoto,
                        label: context.tr.pickImage,
                        isRequired: false,
                        // initialValue: currentUser.profilePhoto != null
                        //     ? [currentUser.profilePhoto]
                        //     : [],
                        onChanged: (images) {
                          if (images != null && images.isNotEmpty) {
                            selectedImagePath.value = images.first.path;
                          } else {
                            selectedImagePath.value = null;
                          }
                        },
                      ),
                    ),

                    AppGaps.gap24,

                    // Name Field
                    BaseTextField(
                      name: FieldsConsts.name,
                      initialValue: currentUser.name,
                      title: context.tr.fullName,
                      hint: context.tr.fullName,
                      useUnderlineBorder: true,
                      validator: (value) => Validations.mustBeNotEmpty(
                        value,
                        emptyMessage: context.tr.fullName,
                      ),
                      realTimeValidator: (value) {
                        final isValid =
                            Validations.mustBeNotEmpty(value) == null;
                        nameValid.value = isValid;
                        return null;
                      },
                    ),

                    AppGaps.gap16,

                    // Phone Number Field
                    BaseTextField(
                      name: FieldsConsts.mobile,
                      initialValue: currentUser.phone,
                      title: context.tr.mobileNumber,
                      hint: context.tr.phoneHint,
                      textInputType: TextInputType.phone,
                      useUnderlineBorder: true,
                      validator: (value) => Validations.palestinianPhoneNumber(
                        value,
                        emptyMessage: context.tr.mobileNumber,
                        invalidMessage: context.tr.invalidPhoneNumber,
                      ),
                      realTimeValidator: (value) {
                        final error = Validations.palestinianPhoneNumber(
                          value,
                          emptyMessage: context.tr.mobileNumber,
                          invalidMessage: context.tr.invalidPhoneNumber,
                        );
                        phoneValid.value = error == null;
                        return error;
                      },
                    ),

                    AppGaps.gap16,

                    // Password Field (Optional)
                    BaseTextField(
                      name: FieldsConsts.password,
                      controller: passwordController,
                      title: context.tr.newPassword,
                      hint: context.tr.newPassword,
                      textInputType: TextInputType.visiblePassword,
                      isObscure: true,
                      isRequired: false,
                      useUnderlineBorder: true,
                      validator: (value) {
                        if (passwordController.text?.isEmpty == true) {
                          return null; // Optional field
                        }
                        return Validations.password(
                          value,
                          emptyPasswordMessage: context.tr.password,
                        );
                      },
                      realTimeValidator: (value) {
                        if (passwordController.text?.isEmpty == true) {
                          passwordValid.value = true;
                          return null;
                        }
                        final error = Validations.password(
                          value,
                          emptyPasswordMessage: context.tr.password,
                        );
                        passwordValid.value = error == null;
                        return error;
                      },
                    ),

                    AppGaps.gap16,

                    // Confirm Password Field (Optional)
                    BaseTextField(
                      name: FieldsConsts.confirmPassword,
                      title: context.tr.confirmPassword,
                      hint: context.tr.confirmPassword,
                      controller: confirmPasswordController,
                      textInputType: TextInputType.visiblePassword,
                      isObscure: true,
                      useUnderlineBorder: true,
                      isRequired: false,
                      validator: (value) {
                        // final password = formKey.currentState
                        //     ?.fields[FieldsConsts.password]?.value;

                        if (passwordController.text?.isEmpty == true &&
                            value?.isEmpty == true) {
                          return null;
                        }
                        return Validations.confirmPassword(
                          value,
                          passwordController.text,
                          emptyMessage: context.tr.confirmPassword,
                          mismatchMessage: context.tr.passwordsDoNotMatch,
                        );
                      },
                      realTimeValidator: (value) {
                        if (passwordController.text?.isEmpty == true &&
                            value?.isEmpty == true) {
                          confirmPasswordValid.value = true;
                          return null;
                        }
                        final error = Validations.confirmPassword(
                          value,
                          passwordController.text,
                          emptyMessage: context.tr.confirmPassword,
                          mismatchMessage: context.tr.passwordsDoNotMatch,
                        );
                        confirmPasswordValid.value = error == null;
                        return error;
                      },
                    ),

                    AppGaps.gap24,

                    // Save Button
                    SizedBox(
                      height: 50,
                      width: double.infinity,
                      child: Button(
                        onPressed:
                            isFormValid.value && !authController.isLoading
                                ? saveProfile
                                : null,
                        label: context.tr.saveChanges,
                        isLoading: authController.isLoading,
                        loadingWidget: LoadingWidget(),
                      ),
                    ),

                    AppGaps.gap48,
                  ],
                ),
              ),
            )),
          ),
        ),
      ),
    );
  }
}
