import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/html_content_dialog.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/auth/models/user_model.dart';
import 'package:luster/src/screens/auth/providers/auth_providers.dart';
import 'package:luster/src/screens/profile/view/edit_profile.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final currentUser = UserModel.currentUser();

    void showLogoutDialog() {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(context.tr.logout),
          content: Text(context.tr.logoutConfirmation),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(context.tr.cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                authController.logout();
              },
              child: Text(context.tr.logout),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        centerTitle: true,
        title: Text(
          context.tr.profile,
          style: AppTextStyles.title,
        ),
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        actions: [
          IconButton(
            onPressed: () => const EditProfileScreen().navigate,
            icon: const Icon(Icons.edit),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Profile Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  // Profile Image
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: ColorManager.darkGrey.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(50),
                      child:
                      // currentUser.profilePhoto != null &&
                      //         currentUser.profilePhoto!.isNotEmpty
                      //     ? BaseCachedImage(
                      //         currentUser.profilePhoto!,
                      //         fit: BoxFit.cover,
                      //         width: 100,
                      //         height: 100,
                      //       )
                      //     :
                      Icon(
                              CupertinoIcons.person,
                              size: 50,
                              color: Colors.white,
                            ),
                    ),
                  ),

                  AppGaps.gap16,

                  // User Name
                  Text(
                    currentUser.name ?? 'المستخدم',
                    style: AppTextStyles.title.copyWith(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  AppGaps.gap8,

                  // Phone Number
                  Text(
                    currentUser.phone ?? '',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            AppGaps.gap24,

            // Menu Items
            _buildMenuItem(
              icon: Icons.privacy_tip_outlined,
              title: context.tr.privacyPolicy,
              onTap: () {
                HtmlContentDialog.show(context, PageType.privacyPolicy);
              },
            ),

            _buildMenuItem(
              icon: Icons.info_outline,
              title: context.tr.aboutUs,
              onTap: () {
                HtmlContentDialog.show(context, PageType.aboutUs);
              },
            ),

            AppGaps.gap48,

            // Logout Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: showLogoutDialog,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(context.tr.logout,
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: Colors.white,
                        )),
                    AppGaps.gap12,
                    const Icon(
                      Icons.logout,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),

            AppGaps.gap48,
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: ColorManager.primaryColor,
        ),
        title: Text(
          title,
          style: AppTextStyles.bodyLarge,
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        tileColor: Colors.grey[50],
      ),
    );
  }
}
