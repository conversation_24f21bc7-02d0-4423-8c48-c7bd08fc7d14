import 'package:luster/src/screens/products/models/plan_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:xr_helper/xr_helper.dart';

/// Repository for managing product plans in Supabase
class PlansRepository with BaseRepository {
  final SupabaseClient supabase;

  PlansRepository({
    required this.supabase,
  });

  /// Get plan by product documentId
  Future<PlanModel?> getPlanByProductId(String productDocumentId) async {
    return baseFunction(
      () async {
        final response = await supabase
            .from('product_plans')
            .select()
            .eq('product_document_id', productDocumentId)
            .order('plan_index', ascending: false)
            .limit(1)
            .maybeSingle();

        if (response == null) {
          return null;
        }

        return PlanModel.fromJson(response);
      },
    );
  }

  /// Get all plans for a product (all versions)
  Future<List<PlanModel>> getAllPlansForProduct(String productDocumentId) async {
    return baseFunction(
      () async {
        final response = await supabase
            .from('product_plans')
            .select()
            .eq('product_document_id', productDocumentId)
            .order('plan_index', ascending: false);

        return (response as List)
            .map((json) => PlanModel.fromJson(json))
            .toList();
      },
    );
  }

  /// Create a new plan
  Future<PlanModel> createPlan(PlanModel plan) async {
    return baseFunction(
      () async {
        // Get the next plan index for this product
        final existingPlans = await getAllPlansForProduct(plan.productDocumentId);
        final nextIndex = existingPlans.isEmpty ? 1 : existingPlans.first.planIndex + 1;

        final planData = {
          'product_document_id': plan.productDocumentId,
          'plan_index': nextIndex,
          'entries': plan.entries.map((e) => e.toJson()).toList(),
          'created_by_admin': plan.createdByAdmin,
        };

        final response = await supabase
            .from('product_plans')
            .insert(planData)
            .select()
            .single();

        return PlanModel.fromJson(response);
      },
    );
  }

  /// Update an existing plan
  Future<PlanModel> updatePlan(PlanModel plan) async {
    return baseFunction(
      () async {
        final planData = {
          'entries': plan.entries.map((e) => e.toJson()).toList(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        final response = await supabase
            .from('product_plans')
            .update(planData)
            .eq('id', plan.id!)
            .select()
            .single();

        return PlanModel.fromJson(response);
      },
    );
  }

  /// Delete a plan
  Future<bool> deletePlan(String planId) async {
    return baseFunction(
      () async {
        await supabase.from('product_plans').delete().eq('id', planId);

        return true;
      },
    );
  }
}

