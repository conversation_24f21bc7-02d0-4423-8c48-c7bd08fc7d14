import 'package:luster/src/core/consts/network/api_endpoints.dart';
import 'package:luster/src/screens/products/models/category_model.dart';
import 'package:luster/src/screens/products/models/product_model.dart';
import 'package:xr_helper/xr_helper.dart';

/// Repository for fetching products and categories from Strapi
class ProductsRepository with BaseRepository {
  final BaseApiServices networkApiService;

  ProductsRepository({
    required this.networkApiService,
  });

  /// Get all categories from Strapi
  Future<List<CategoryModel>> getCategories() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.categories;

        final response = await networkApiService.getResponse(url);

        final categoriesResponse = CategoriesResponse.fromJson(response);

        return categoriesResponse.data;
      },
    );
  }

  /// Get all products from Strapi
  Future<List<ProductModel>> getProducts() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.products;

        final response = await networkApiService.getResponse(url);

        final productsResponse = ProductsResponse.fromJson(response);

        return productsResponse.data;
      },
    );
  }

  /// Get products by category
  Future<List<ProductModel>> getProductsByCategory(String categoryDocumentId) async {
    return baseFunction(
      () async {
        final url =
            '${ApiEndpoints.products}&filters[product_category][documentId][\$eq]=$categoryDocumentId';

        final response = await networkApiService.getResponse(url);

        final productsResponse = ProductsResponse.fromJson(response);

        return productsResponse.data;
      },
    );
  }

  /// Get product by documentId
  Future<ProductModel?> getProductByDocumentId(String documentId) async {
    return baseFunction(
      () async {
        final url =
            '${ApiEndpoints.products}&filters[documentId][\$eq]=$documentId';

        final response = await networkApiService.getResponse(url);

        final productsResponse = ProductsResponse.fromJson(response);

        if (productsResponse.data.isEmpty) {
          return null;
        }

        return productsResponse.data.first;
      },
    );
  }
}

