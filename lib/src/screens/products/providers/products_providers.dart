import 'package:get_storage/get_storage.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/shared/providers/network_api_service_provider.dart';
import 'package:luster/src/screens/products/controllers/plans_controller.dart';
import 'package:luster/src/screens/products/controllers/products_controller.dart';
import 'package:luster/src/screens/products/models/category_model.dart';
import 'package:luster/src/screens/products/models/plan_model.dart';
import 'package:luster/src/screens/products/models/product_model.dart';
import 'package:luster/src/screens/products/models/scanned_product_model.dart';
import 'package:luster/src/screens/products/repositories/plans_repository.dart';
import 'package:luster/src/screens/products/repositories/products_repository.dart';
import 'package:luster/src/screens/products/services/scanned_products_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// ============================================================================
// REPOSITORIES
// ============================================================================

/// Products Repository Provider
final productsRepositoryProvider = Provider<ProductsRepository>((ref) {
  return ProductsRepository(
    networkApiService: ref.read(networkServiceProvider),
  );
});

/// Plans Repository Provider
final plansRepositoryProvider = Provider<PlansRepository>((ref) {
  return PlansRepository(
    supabase: Supabase.instance.client,
  );
});

/// Scanned Products Service Provider
final scannedProductsServiceProvider = Provider<ScannedProductsService>((ref) {
  return ScannedProductsService(GetStorage());
});

// ============================================================================
// CONTROLLERS
// ============================================================================

/// Products Controller Provider
final productsControllerProvider = Provider<ProductsController>((ref) {
  return ProductsController(
    productsRepo: ref.read(productsRepositoryProvider),
  );
});

/// Plans Controller Provider
final plansControllerProvider = Provider<PlansController>((ref) {
  return PlansController(
    plansRepo: ref.read(plansRepositoryProvider),
  );
});

// ============================================================================
// FUTURE PROVIDERS (for data fetching)
// ============================================================================

/// Get all categories
final getCategoriesFutureProvider = FutureProvider<List<CategoryModel>>((ref) async {
  return await ref.read(productsControllerProvider).getCategories();
});

/// Get all products
final getProductsFutureProvider = FutureProvider<List<ProductModel>>((ref) async {
  return await ref.read(productsControllerProvider).getProducts();
});

/// Get products by category
final getProductsByCategoryProvider =
    FutureProvider.family<List<ProductModel>, String>((ref, categoryDocumentId) async {
  return await ref.read(productsControllerProvider).getProductsByCategory(categoryDocumentId);
});

/// Get product by documentId
final getProductByDocumentIdProvider =
    FutureProvider.family<ProductModel?, String>((ref, documentId) async {
  return await ref.read(productsControllerProvider).getProductByDocumentId(documentId);
});

/// Get plan by product documentId
final getPlanByProductIdProvider =
    FutureProvider.family<PlanModel?, String>((ref, productDocumentId) async {
  return await ref.read(plansControllerProvider).getPlanByProductId(productDocumentId);
});

/// Get all plans for a product
final getAllPlansForProductProvider =
    FutureProvider.family<List<PlanModel>, String>((ref, productDocumentId) async {
  return await ref.read(plansControllerProvider).getAllPlansForProduct(productDocumentId);
});

// ============================================================================
// STATE PROVIDERS (for scanned products)
// ============================================================================

/// Scanned products state provider
final scannedProductsProvider = StateNotifierProvider<ScannedProductsNotifier, List<ScannedProductModel>>((ref) {
  return ScannedProductsNotifier(ref.read(scannedProductsServiceProvider));
});

/// Scanned products notifier
class ScannedProductsNotifier extends StateNotifier<List<ScannedProductModel>> {
  final ScannedProductsService _service;

  ScannedProductsNotifier(this._service) : super([]) {
    _loadScannedProducts();
  }

  /// Load scanned products from storage
  void _loadScannedProducts() {
    state = _service.getAllScannedProducts();
  }

  /// Add a scanned product
  Future<void> addScannedProduct(ScannedProductModel scannedProduct) async {
    await _service.saveScannedProduct(scannedProduct);
    _loadScannedProducts();
  }

  /// Update progress for a scanned product
  Future<void> updateProgress(String productDocumentId, Map<String, bool> completedEntries) async {
    await _service.updateProgress(productDocumentId, completedEntries);
    _loadScannedProducts();
  }

  /// Delete a scanned product
  Future<void> deleteScannedProduct(String productDocumentId) async {
    await _service.deleteScannedProduct(productDocumentId);
    _loadScannedProducts();
  }

  /// Clear all scanned products
  Future<void> clearAll() async {
    await _service.clearAll();
    _loadScannedProducts();
  }

  /// Refresh scanned products
  void refresh() {
    _loadScannedProducts();
  }
}

