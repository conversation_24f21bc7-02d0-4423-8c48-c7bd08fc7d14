import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';

/// Category model for Strapi product categories with Arabic/English support
class CategoryModel extends Equatable {
  final int? id;
  final String documentId;
  final String? name;
  final String? nameAr;
  final String? description;
  final int? sort;
  final String? image;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const CategoryModel({
    this.id,
    required this.documentId,
    this.name,
    this.nameAr,
    this.description,
    this.sort,
    this.image,
    this.createdAt,
    this.updatedAt,
  });

  /// Get category name based on current language
  String nameByLang(BuildContext context) {
    final isEng = context.isEnglish;
    return isEng ? (name ?? nameAr ?? '') : (nameAr ?? name ?? '');
  }

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'],
      documentId: json['documentId'] ?? '',
      name: json['name'],
      nameAr: json['name_ar'],
      description: json['description'],
      sort: json['sort'],
      image: _extractImageUrl(json['image']),
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : null,
    );
  }

  static String? _extractImageUrl(dynamic imageData) {
    if (imageData == null) return null;
    if (imageData is String) return imageData;
    if (imageData is Map) {
      // Handle Strapi media format
      if (imageData['url'] != null) {
        final url = imageData['url'] as String;
        // If URL is relative, prepend Strapi base URL
        if (url.startsWith('/')) {
          return 'https://backend.idea2app.tech$url';
        }
        return url;
      }
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'name': name,
      'name_ar': nameAr,
      'description': description,
      'sort': sort,
      'image': image,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        documentId,
        name,
        nameAr,
        description,
        sort,
        image,
        createdAt,
        updatedAt,
      ];
}

/// Response model for categories list from Strapi
class CategoriesResponse extends Equatable {
  final List<CategoryModel> data;
  final Map<String, dynamic>? meta;

  const CategoriesResponse({
    required this.data,
    this.meta,
  });

  factory CategoriesResponse.fromJson(Map<String, dynamic> json) {
    final dataList = json['data'] as List? ?? [];
    return CategoriesResponse(
      data: dataList.map((e) => CategoryModel.fromJson(e)).toList(),
      meta: json['meta'],
    );
  }

  @override
  List<Object?> get props => [data, meta];
}
