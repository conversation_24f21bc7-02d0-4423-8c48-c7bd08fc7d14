import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'plan_entry_model.dart';

/// Product plan model stored in Supabase
class PlanModel extends Equatable {
  final String? id; // UUID from Supabase
  final String productDocumentId; // Product's documentId from Strapi
  final int planIndex; // Starting day number or plan sequence
  final List<PlanEntryModel> entries; // List of plan entries
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PlanModel({
    this.id,
    required this.productDocumentId,
    required this.planIndex,
    required this.entries,
    this.createdAt,
    this.updatedAt,
  });

  factory PlanModel.fromJson(Map<String, dynamic> json) {
    final entriesData = json['entries'];
    List<PlanEntryModel> entries = [];

    if (entriesData is List) {
      entries = entriesData.map((e) => PlanEntryModel.fromJson(e)).toList();
    } else if (entriesData is String) {
      // Handle JSON string from Supabase
      try {
        final decoded = jsonDecode(entriesData) as List;
        entries = decoded.map((e) => PlanEntryModel.fromJson(e)).toList();
      } catch (e) {
        // If parsing fails, return empty list
        entries = [];
      }
    }

    return PlanModel(
      id: json['id'],
      productDocumentId: json['product_document_id'] ?? '',
      planIndex: json['plan_index'] ?? 1,
      entries: entries,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'product_document_id': productDocumentId,
      'plan_index': planIndex,
      'entries': entries.map((e) => e.toJson()).toList(),
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
    };
  }

  /// Get total number of days in the plan
  int get totalDays {
    if (entries.isEmpty) return 0;
    return entries.map((e) => e.day).reduce((a, b) => a > b ? a : b);
  }

  /// Get entries for a specific day
  List<PlanEntryModel> getEntriesForDay(int day) {
    return entries.where((e) => e.day == day).toList();
  }

  /// Create a copy with modified fields
  PlanModel copyWith({
    String? id,
    String? productDocumentId,
    int? planIndex,
    List<PlanEntryModel>? entries,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PlanModel(
      id: id ?? this.id,
      productDocumentId: productDocumentId ?? this.productDocumentId,
      planIndex: planIndex ?? this.planIndex,
      entries: entries ?? this.entries,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        productDocumentId,
        planIndex,
        entries,
        createdAt,
        updatedAt,
      ];
}
