import 'package:equatable/equatable.dart';
import 'plan_model.dart';
import 'product_model.dart';

/// Model for a scanned product stored locally
class ScannedProductModel extends Equatable {
  final String productDocumentId;
  final ProductModel? product; // Product details from Strapi
  final PlanModel? plan; // Plan details from Supabase
  final DateTime scannedAt; // When the product was scanned
  final Map<String, bool>? completedEntries; // Track completed plan entries (key: "day_time")
  final int? currentDayIndex; // Current day in the plan (calculated)

  const ScannedProductModel({
    required this.productDocumentId,
    this.product,
    this.plan,
    required this.scannedAt,
    this.completedEntries,
    this.currentDayIndex,
  });

  factory ScannedProductModel.fromJson(Map<String, dynamic> json) {
    return ScannedProductModel(
      productDocumentId: json['productDocumentId'] ?? '',
      product: json['product'] != null
          ? ProductModel.fromJson(json['product'])
          : null,
      plan: json['plan'] != null ? PlanModel.fromJson(json['plan']) : null,
      scannedAt: json['scannedAt'] != null
          ? DateTime.parse(json['scannedAt'])
          : DateTime.now(),
      completedEntries: json['completedEntries'] != null
          ? Map<String, bool>.from(json['completedEntries'])
          : null,
      currentDayIndex: json['currentDayIndex'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productDocumentId': productDocumentId,
      if (product != null) 'product': product!.toJson(),
      if (plan != null) 'plan': plan!.toJson(),
      'scannedAt': scannedAt.toIso8601String(),
      if (completedEntries != null) 'completedEntries': completedEntries,
      if (currentDayIndex != null) 'currentDayIndex': currentDayIndex,
    };
  }

  /// Calculate the current day index based on scannedAt and current time
  int calculateCurrentDay() {
    final now = DateTime.now();
    final difference = now.difference(scannedAt);
    final daysPassed = difference.inDays + 1; // +1 because day 1 is the scan day
    return daysPassed;
  }

  /// Check if a specific entry is completed
  bool isEntryCompleted(int day, String time) {
    if (completedEntries == null) return false;
    final key = '${day}_$time';
    return completedEntries![key] ?? false;
  }

  /// Mark an entry as completed
  ScannedProductModel markEntryCompleted(int day, String time, bool completed) {
    final updatedCompletedEntries = Map<String, bool>.from(completedEntries ?? {});
    final key = '${day}_$time';
    updatedCompletedEntries[key] = completed;

    return copyWith(completedEntries: updatedCompletedEntries);
  }

  /// Get progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (plan == null || plan!.entries.isEmpty) return 0.0;
    if (completedEntries == null || completedEntries!.isEmpty) return 0.0;

    final totalEntries = plan!.entries.length;
    final completedCount =
        completedEntries!.values.where((completed) => completed).length;

    return completedCount / totalEntries;
  }

  /// Create a copy with modified fields
  ScannedProductModel copyWith({
    String? productDocumentId,
    ProductModel? product,
    PlanModel? plan,
    DateTime? scannedAt,
    Map<String, bool>? completedEntries,
    int? currentDayIndex,
  }) {
    return ScannedProductModel(
      productDocumentId: productDocumentId ?? this.productDocumentId,
      product: product ?? this.product,
      plan: plan ?? this.plan,
      scannedAt: scannedAt ?? this.scannedAt,
      completedEntries: completedEntries ?? this.completedEntries,
      currentDayIndex: currentDayIndex ?? this.currentDayIndex,
    );
  }

  @override
  List<Object?> get props => [
        productDocumentId,
        product,
        plan,
        scannedAt,
        completedEntries,
        currentDayIndex,
      ];
}

