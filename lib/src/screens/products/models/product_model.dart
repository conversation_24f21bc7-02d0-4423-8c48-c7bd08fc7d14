import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'category_model.dart';

/// Product model for Strapi products with Arabic/English support
class ProductModel extends Equatable {
  final int? id;
  final String? documentId;
  final String? englishTitle;
  final String? arabicTitle;
  final String? englishDescription;
  final String? arabicDescription;
  final num? totalPrice;
  final num? salePrice;
  final bool isSale;
  final String? thumbnail;
  final List<String>? images;
  final CategoryModel? category;
  final int? sort;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ProductModel({
    this.id,
    this.documentId,
    this.englishTitle,
    this.arabicTitle,
    this.englishDescription,
    this.arabicDescription,
    this.totalPrice,
    this.salePrice,
    this.isSale = false,
    this.thumbnail,
    this.images,
    this.category,
    this.sort,
    this.createdAt,
    this.updatedAt,
  });

  /// Get actual price (sale price if on sale, otherwise total price)
  num get actualPrice => isSale ? (salePrice ?? 0) : (totalPrice ?? 0);

  /// Get product name based on current language
  String nameByLang(BuildContext context) {
    final isEng = context.isEnglish;
    return isEng
        ? (englishTitle ?? arabicTitle ?? '')
        : (arabicTitle ?? englishTitle ?? '');
  }

  /// Get product description based on current language
  String? descriptionByLang(BuildContext context) {
    final isEng = context.isEnglish;
    return isEng
        ? (englishDescription ?? arabicDescription)
        : (arabicDescription ?? englishDescription);
  }

  /// Get the primary image URL (thumbnail or first image)
  String? get primaryImage =>
      thumbnail ?? (images?.isNotEmpty == true ? images!.first : null);

  /// Get the buy URL for this product
  String get buyUrl => 'https://i2shop.store/luster?landing=$documentId';

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    final imagesData = json['images'];
    final imagesList = _extractImages(imagesData);
    final thumbnailUrl =
        imagesList?.isNotEmpty == true ? imagesList!.first : null;

    // Handle categories - can be a list or single object
    final categoriesData = json['categories'];
    CategoryModel? category;
    if (categoriesData != null) {
      if (categoriesData is List && categoriesData.isNotEmpty) {
        category = CategoryModel.fromJson(
            Map<String, dynamic>.from(categoriesData.first as Map));
      } else if (categoriesData is Map) {
        category =
            CategoryModel.fromJson(Map<String, dynamic>.from(categoriesData));
      }
    }

    return ProductModel(
      id: json['id'],
      documentId: json['documentId'],
      englishTitle: json['title'],
      arabicTitle: json['title_ar'],
      englishDescription: json['description'],
      arabicDescription: json['description_ar'],
      totalPrice: json['price'] ?? 0,
      salePrice: json['sale_price'] ?? 0,
      isSale: json['is_sale'] ?? false,
      thumbnail: thumbnailUrl,
      images: imagesList,
      category: category,
      sort: json['sort'],
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.tryParse(json['updatedAt'])
          : null,
    );
  }

  static String? _extractImageUrl(dynamic imageData) {
    if (imageData == null) return null;
    if (imageData is String) return imageData;
    if (imageData is Map) {
      if (imageData['url'] != null) {
        final url = imageData['url'] as String;
        if (url.startsWith('/')) {
          return 'https://backend.idea2app.tech$url';
        }
        return url;
      }
    }
    return null;
  }

  static List<String>? _extractImages(dynamic imagesData) {
    if (imagesData == null) return null;
    if (imagesData is! List) return null;

    final List<String> imageUrls = [];
    for (var item in imagesData) {
      final url = _extractImageUrl(item);
      if (url != null) imageUrls.add(url);
    }
    return imageUrls.isEmpty ? null : imageUrls;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'documentId': documentId,
      'title': englishTitle,
      'title_ar': arabicTitle,
      'description': englishDescription,
      'description_ar': arabicDescription,
      'price': totalPrice,
      'sale_price': salePrice,
      'is_sale': isSale,
      'thumbnail': thumbnail,
      'images': images,
      'category': category?.toJson(),
      'sort': sort,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        documentId,
        englishTitle,
        arabicTitle,
        englishDescription,
        arabicDescription,
        totalPrice,
        salePrice,
        isSale,
        thumbnail,
        images,
        category,
        sort,
        createdAt,
        updatedAt,
      ];
}

/// Response model for products list from Strapi
class ProductsResponse extends Equatable {
  final List<ProductModel> data;
  final Map<String, dynamic>? meta;

  const ProductsResponse({
    required this.data,
    this.meta,
  });

  factory ProductsResponse.fromJson(Map<String, dynamic> json) {
    final dataList = json['data'] as List? ?? [];
    return ProductsResponse(
      data: dataList.map((e) => ProductModel.fromJson(e)).toList(),
      meta: json['meta'],
    );
  }

  @override
  List<Object?> get props => [data, meta];
}
