import 'package:equatable/equatable.dart';

/// Represents a single entry in a product usage plan
class PlanEntryModel extends Equatable {
  final int day; // Day number (1, 2, 3, etc.)
  final String time; // Time in HH:mm format (e.g., "13:00")
  final String key; // Action key (e.g., "Shampoo", "Conditioner")
  final String value; // Action description (e.g., "Clean hair", "Moisturize")
  final String? description; // Optional: detailed description for the entry
  final int? repeatCount; // Optional: how many times to repeat this action

  const PlanEntryModel({
    required this.day,
    required this.time,
    required this.key,
    required this.value,
    this.description,
    this.repeatCount,
  });

  factory PlanEntryModel.fromJson(Map<String, dynamic> json) {
    return PlanEntryModel(
      day: json['day'] ?? 1,
      time: json['time'] ?? '09:00',
      key: json['key'] ?? '',
      value: json['value'] ?? '',
      description: json['description'],
      repeatCount: json['repeatCount'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'time': time,
      'key': key,
      'value': value,
      if (description != null) 'description': description,
      if (repeatCount != null) 'repeatCount': repeatCount,
    };
  }

  /// Create a copy with modified fields
  PlanEntryModel copyWith({
    int? day,
    String? time,
    String? key,
    String? value,
    String? description,
    int? repeatCount,
  }) {
    return PlanEntryModel(
      day: day ?? this.day,
      time: time ?? this.time,
      key: key ?? this.key,
      value: value ?? this.value,
      description: description ?? this.description,
      repeatCount: repeatCount ?? this.repeatCount,
    );
  }

  @override
  List<Object?> get props => [day, time, key, value, description, repeatCount];
}
