import 'package:luster/src/screens/products/models/plan_model.dart';
import 'package:luster/src/screens/products/repositories/plans_repository.dart';
import 'package:xr_helper/xr_helper.dart';

/// Controller for managing product plans
class PlansController extends BaseVM {
  final PlansRepository plansRepo;

  PlansController({
    required this.plansRepo,
  });

  /// Get plan by product documentId
  Future<PlanModel?> getPlanByProductId(String productDocumentId) async {
    return await baseFunction(
      () async {
        return await plansRepo.getPlanByProductId(productDocumentId);
      },
    );
  }

  /// Get all plans for a product
  Future<List<PlanModel>> getAllPlansForProduct(String productDocumentId) async {
    return await baseFunction(
      () async {
        return await plansRepo.getAllPlansForProduct(productDocumentId);
      },
    );
  }

  /// Create a new plan
  Future<PlanModel> createPlan(PlanModel plan) async {
    return await baseFunction(
      () async {
        return await plansRepo.createPlan(plan);
      },
    );
  }

  /// Update an existing plan
  Future<PlanModel> updatePlan(PlanModel plan) async {
    return await baseFunction(
      () async {
        return await plansRepo.updatePlan(plan);
      },
    );
  }

  /// Delete a plan
  Future<bool> deletePlan(String planId) async {
    return await baseFunction(
      () async {
        return await plansRepo.deletePlan(planId);
      },
    );
  }
}

