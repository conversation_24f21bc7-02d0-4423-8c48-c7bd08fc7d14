import 'package:luster/src/screens/products/models/category_model.dart';
import 'package:luster/src/screens/products/models/product_model.dart';
import 'package:luster/src/screens/products/repositories/products_repository.dart';
import 'package:xr_helper/xr_helper.dart';

/// Controller for managing products and categories
class ProductsController extends BaseVM {
  final ProductsRepository productsRepo;

  ProductsController({
    required this.productsRepo,
  });

  /// Get all categories
  Future<List<CategoryModel>> getCategories() async {
    return await baseFunction(
      () async {
        return await productsRepo.getCategories();
      },
    );
  }

  /// Get all products
  Future<List<ProductModel>> getProducts() async {
    return await baseFunction(
      () async {
        return await productsRepo.getProducts();
      },
    );
  }

  /// Get products by category
  Future<List<ProductModel>> getProductsByCategory(String categoryDocumentId) async {
    return await baseFunction(
      () async {
        return await productsRepo.getProductsByCategory(categoryDocumentId);
      },
    );
  }

  /// Get product by documentId
  Future<ProductModel?> getProductByDocumentId(String documentId) async {
    return await baseFunction(
      () async {
        return await productsRepo.getProductByDocumentId(documentId);
      },
    );
  }
}

