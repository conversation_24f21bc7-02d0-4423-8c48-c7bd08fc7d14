import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/category_model.dart';
import 'package:shimmer/shimmer.dart';
import 'package:xr_helper/xr_helper.dart';

/// Beautiful category chip with circular avatar
class CategoryChip extends StatelessWidget {
  final CategoryModel category;
  final bool isSelected;
  final VoidCallback? onTap;

  const CategoryChip({
    super.key,
    required this.category,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 8.h,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? ColorManager.primaryColor
              : ColorManager.primaryOpacityContainer,
          borderRadius: BorderRadius.circular(24.r),
          border: Border.all(
            color: isSelected
                ? ColorManager.primaryColor
                : ColorManager.primaryColor.withOpacity(0.2),
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: ColorManager.primaryColor.withOpacity(0.3),
                    spreadRadius: 0,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Category Image (if available)
            if (category.image != null) ...[
              CircleAvatar(
                radius: 14.r,
                backgroundColor: Colors.white,
                child: ClipOval(
                  child: CachedNetworkImage(
                    imageUrl: category.image!,
                    width: 24.w,
                    height: 24.h,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: ColorManager.shimmerBaseColor,
                      highlightColor: Colors.white,
                      child: Container(color: ColorManager.shimmerBaseColor),
                    ),
                    errorWidget: (context, url, error) => Icon(
                      Icons.category,
                      size: 16.sp,
                      color: ColorManager.primaryColor,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
            ],

            // Category Name
            Text(
              category.nameByLang(context),
              style: AppTextStyles.labelMedium.copyWith(
                color: isSelected ? Colors.white : ColorManager.primaryColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Shimmer loading placeholder for category chip
class CategoryChipShimmer extends StatelessWidget {
  const CategoryChipShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: ColorManager.shimmerBaseColor,
      highlightColor: Colors.white,
      child: Container(
        width: 100.w,
        height: 36.h,
        decoration: BoxDecoration(
          color: ColorManager.shimmerBaseColor,
          borderRadius: BorderRadius.circular(24.r),
        ),
      ),
    );
  }
}
