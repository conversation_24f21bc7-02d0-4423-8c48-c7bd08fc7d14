import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/product_model.dart';
import 'package:shimmer/shimmer.dart';
import 'package:xr_helper/xr_helper.dart';

/// Modern product card with shimmer loading and beautiful design
class ProductCard extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final VoidCallback? onBuyTap;
  final bool showBuyButton;

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onBuyTap,
    this.showBuyButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.radius12),
          border: Border.all(
            color: ColorManager.primaryColor.withOpacity(0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: ColorManager.primaryColor.withOpacity(0.08),
              spreadRadius: 0,
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            ClipRRect(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(AppRadius.radius12),
              ),
              child: SizedBox(
                height: 200,
                child: product.primaryImage != null
                    ? CachedNetworkImage(
                        imageUrl: product.primaryImage!,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        placeholder: (context, url) =>
                            _buildShimmerPlaceholder(),
                        errorWidget: (context, url, error) =>
                            _buildErrorPlaceholder(),
                      )
                    : _buildErrorPlaceholder(),
              ),
            ),

            // Product Info
            Padding(
              padding: const EdgeInsets.all(AppSpaces.padding12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Name
                  Text(
                    product.nameByLang(context),
                    style: AppTextStyles.labelLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorManager.primaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  if (product.descriptionByLang(context) != null) ...[
                    AppGaps.gap4,
                    Text(
                      product.descriptionByLang(context)?.trim() ?? '',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: ColorManager.greyText,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],

                  if (product.actualPrice > 0) ...[
                    AppGaps.gap8,
                    Row(
                      children: [
                        if (product.isSale && product.totalPrice != null) ...[
                          Text(
                            '${product.totalPrice!.toStringAsFixed(2)} EGP',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.greyText,
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                          AppGaps.gap8,
                        ],
                        Text(
                          '${product.actualPrice.toStringAsFixed(2)} EGP',
                          style: AppTextStyles.labelMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: product.isSale
                                ? ColorManager.errorColor
                                : ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],

                  if (showBuyButton) ...[
                    AppGaps.gap8,
                    SizedBox(
                      width: double.infinity,
                      height: 36.h,
                      child: Button(
                        label: context.tr.buy,
                        onPressed: onBuyTap,
                        color: ColorManager.primaryColor,
                        radius: AppRadius.radius8,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: ColorManager.shimmerBaseColor,
      highlightColor: Colors.white,
      child: Container(
        color: ColorManager.shimmerBaseColor,
      ),
    );
  }

  Widget _buildErrorPlaceholder() {
    return Container(
      color: ColorManager.primaryOpacityContainer,
      child: const Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          size: 48,
          color: ColorManager.greyIcon,
        ),
      ),
    );
  }
}

/// Shimmer loading placeholder for product card
class ProductCardShimmer extends StatelessWidget {
  const ProductCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: ColorManager.shimmerBaseColor,
      highlightColor: Colors.white,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppRadius.radius12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image placeholder
            AspectRatio(
              aspectRatio: 1,
              child: Container(
                decoration: BoxDecoration(
                  color: ColorManager.shimmerBaseColor,
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(AppRadius.radius12),
                  ),
                ),
              ),
            ),

            // Content placeholder
            Padding(
              padding: const EdgeInsets.all(AppSpaces.padding12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 16,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: ColorManager.shimmerBaseColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  AppGaps.gap8,
                  Container(
                    height: 12,
                    width: 100,
                    decoration: BoxDecoration(
                      color: ColorManager.shimmerBaseColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  AppGaps.gap8,
                  Container(
                    height: 14,
                    width: 60,
                    decoration: BoxDecoration(
                      color: ColorManager.shimmerBaseColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
