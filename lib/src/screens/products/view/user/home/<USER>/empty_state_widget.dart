import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

/// Empty state widget for when no scanned products are available
class EmptyStateWidget extends StatelessWidget {
  const EmptyStateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(16),
            margin: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ColorManager.primaryOpacityContainer,
              borderRadius: BorderRadius.circular(AppRadius.radius12),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryOpacityContainer,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.qr_code_scanner_rounded,
                    color: ColorManager.primaryColor,
                  ),
                ),
                AppGaps.gap8,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.scanToStart,
                      style: AppTextStyles.subTitle.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ColorManager.primaryColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    AppGaps.gap4,
                    Text(
                      context.tr.scanProductToStart,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: ColorManager.greyText,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                const Spacer(),
                const Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: ColorManager.primaryColor,
                ),
                AppGaps.gap8,
              ],
            ),
          ),
        ],
      ),
    );
  }
}

