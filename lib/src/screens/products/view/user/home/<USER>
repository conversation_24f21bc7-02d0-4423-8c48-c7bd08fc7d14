import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/scanned_product_model.dart';
import 'package:luster/src/screens/products/providers/products_providers.dart';
import 'package:luster/src/screens/products/view/user/home/<USER>/empty_state_widget.dart';
import 'package:luster/src/screens/products/view/user/home/<USER>/scanned_products_list_widget.dart';
import 'package:luster/src/screens/products/view/user/qr_scanner_screen.dart';
import 'package:luster/src/screens/products/view/user/store_screen.dart';
import 'package:xr_helper/xr_helper.dart';

/// User home screen showing scanned products
class UserHomeScreen extends HookConsumerWidget {
  const UserHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Load from GetStorage via StateNotifier
    final scannedProducts = ref.watch(scannedProductsProvider);

    return Scaffold(
      backgroundColor: ColorManager.backgroundColor,
      appBar: BaseAppBar(
        withBackButton: false,
        title: context.tr.home,
        actions: [
          if (scannedProducts.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.refresh_rounded),
              onPressed: () {
                ref.read(scannedProductsProvider.notifier).refresh();
              },
            ),
        ],
      ),
      body: Column(
        children: [
          scannedProducts.isEmpty
              ? const EmptyStateWidget()
              : ScannedProductsListWidget(products: scannedProducts),
          Expanded(child: StoreScreen()),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          // Navigate to QR scanner
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const QRScannerScreen(),
            ),
          );

          if (result != null && result is ScannedProductModel) {
            // Refresh the list (already added in scanner)
            ref.read(scannedProductsProvider.notifier).refresh();
          }
        },
        backgroundColor: ColorManager.primaryColor,
        icon: const Icon(Icons.qr_code_scanner_rounded),
        label: Text(context.tr.scanProduct),
      ),
    );
  }
}
