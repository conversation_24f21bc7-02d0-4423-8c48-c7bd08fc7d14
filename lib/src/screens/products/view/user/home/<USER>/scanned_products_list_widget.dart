import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/scanned_product_model.dart';
import 'package:luster/src/screens/products/view/user/scanned_product_detail_screen.dart';
import 'package:xr_helper/xr_helper.dart';

/// Widget displaying list of scanned products with progress
class ScannedProductsListWidget extends StatelessWidget {
  final List<ScannedProductModel> products;

  const ScannedProductsListWidget({
    super.key,
    required this.products,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: products.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final scannedProduct = products[index];
        final product = scannedProduct.product;
        final currentDay = scannedProduct.calculateCurrentDay();
        final progress = scannedProduct.progressPercentage;

        return GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ScannedProductDetailScreen(
                  scannedProduct: scannedProduct,
                ),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(AppSpaces.padding16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppRadius.radius12),
              boxShadow: [
                BoxShadow(
                  color: ColorManager.primaryColor.withOpacity(0.08),
                  spreadRadius: 0,
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // Product Icon
                    Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: ColorManager.primaryOpacityContainer,
                        borderRadius: BorderRadius.circular(AppRadius.radius8),
                      ),
                      child: Icon(
                        Icons.inventory_2_rounded,
                        color: ColorManager.primaryColor,
                        size: 24.sp,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    // Product Name
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            product?.nameByLang(context) ??
                                context.tr.unknownProduct,
                            style: AppTextStyles.labelLarge.copyWith(
                              fontWeight: FontWeight.bold,
                              color: ColorManager.primaryColor,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            '${context.tr.currentDay}: $currentDay',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.greyText,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Progress Badge
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.w,
                        vertical: 6.h,
                      ),
                      decoration: BoxDecoration(
                        color: ColorManager.primaryColor,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        '${(progress * 100).toInt()}%',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                AppGaps.gap12,
                // Progress Bar
                ClipRRect(
                  borderRadius: BorderRadius.circular(4.r),
                  child: LinearProgressIndicator(
                    value: progress,
                    backgroundColor: ColorManager.primaryColor.withOpacity(0.1),
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      ColorManager.primaryColor,
                    ),
                    minHeight: 8.h,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

