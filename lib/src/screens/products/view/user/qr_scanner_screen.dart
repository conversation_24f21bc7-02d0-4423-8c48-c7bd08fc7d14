import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/scanned_product_model.dart';
import 'package:luster/src/screens/products/providers/products_providers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:qr_code_dart_scan/qr_code_dart_scan.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/shared/widgets/loading/loading_widget.dart';

/// QR Scanner screen with camera permission handling
class QRScannerScreen extends HookConsumerWidget {
  const QRScannerScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasPermission = useState<bool?>(null);
    final isScanning = useState(true);

    useEffect(() {
      _checkCameraPermission(hasPermission);
      return null;
    }, []);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          context.tr.scanQRCode,
          style: const TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: hasPermission.value == null
          ? const Center(child: LoadingWidget())
          : hasPermission.value == false
              ? _buildPermissionDenied(context)
              : Stack(
                  children: [
                    // QR Scanner
                    QRCodeDartScanView(
                      scanInvertedQRCode: true,
                      typeScan: TypeScan.live,
                      onCapture: (result) {
                        if (isScanning.value && result.text.isNotEmpty) {
                          isScanning.value = false;
                          _handleQRScanned(context, ref, result.text);
                        }
                      },
                    ),

                    // Overlay with scanning frame
                    _buildScannerOverlay(context),

                    // Instructions
                    Positioned(
                      bottom: 40.h,
                      left: 0,
                      right: 0,
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 32.w),
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius:
                              BorderRadius.circular(AppRadius.radius12),
                        ),
                        child: Text(
                          context.tr.positionQRCode,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildScannerOverlay(BuildContext context) {
    return CustomPaint(
      painter: ScannerOverlayPainter(),
      child: Container(),
    );
  }

  Widget _buildPermissionDenied(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt_outlined,
              size: 80.sp,
              color: Colors.white54,
            ),
            AppGaps.gap24,
            Text(
              context.tr.cameraPermissionRequired,
              style: AppTextStyles.headlineSmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            AppGaps.gap12,
            Text(
              context.tr.cameraPermissionPermanentlyDenied,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            AppGaps.gap24,
            SizedBox(
              width: double.infinity,
              child: Button(
                label: context.tr.openSettings,
                onPressed: () => openAppSettings(),
                color: ColorManager.primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkCameraPermission(
      ValueNotifier<bool?> hasPermission) async {
    final status = await Permission.camera.status;
    if (status.isGranted) {
      hasPermission.value = true;
    } else if (status.isDenied) {
      final result = await Permission.camera.request();
      hasPermission.value = result.isGranted;
    } else {
      hasPermission.value = false;
    }
  }

  Future<void> _handleQRScanned(
      BuildContext context, WidgetRef ref, String documentId) async {
    try {
      // Show loading
      showToast(context.tr.loadingProduct);

      // Fetch product from Strapi
      final product =
          await ref.read(getProductByDocumentIdProvider(documentId).future);
      if (product == null) {
        showToast(context.tr.productNotFound, isError: true);
        Navigator.pop(context);
        return;
      }

      // Fetch plan from Supabase
      final plan =
          await ref.read(getPlanByProductIdProvider(documentId).future);

      // Create scanned product model
      final scannedProduct = ScannedProductModel(
        productDocumentId: documentId,
        product: product,
        plan: plan,
        scannedAt: DateTime.now(),
        completedEntries: {},
        currentDayIndex: 0,
      );

      // Save to GetStorage
      await ref
          .read(scannedProductsProvider.notifier)
          .addScannedProduct(scannedProduct);

      showToast(context.tr.productScannedSuccessfully);

      // Pop with the scanned product
      if (context.mounted) {
        Navigator.pop(context, scannedProduct);
      }
    } catch (e) {
      showToast('${context.tr.error}: $e', isError: true);
      if (context.mounted) {
        Navigator.pop(context);
      }
    }
  }
}

/// Custom painter for scanner overlay
class ScannerOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    final scanAreaSize = size.width * 0.7;
    final left = (size.width - scanAreaSize) / 2;
    final top = (size.height - scanAreaSize) / 2;

    // Draw overlay with transparent center
    final path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(left, top, scanAreaSize, scanAreaSize),
          const Radius.circular(16),
        ),
      )
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(path, paint);

    // Draw corner brackets
    final bracketPaint = Paint()
      ..color = ColorManager.primaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    final bracketLength = 30.0;

    // Top-left
    canvas.drawLine(
      Offset(left, top + bracketLength),
      Offset(left, top),
      bracketPaint,
    );
    canvas.drawLine(
      Offset(left, top),
      Offset(left + bracketLength, top),
      bracketPaint,
    );

    // Top-right
    canvas.drawLine(
      Offset(left + scanAreaSize - bracketLength, top),
      Offset(left + scanAreaSize, top),
      bracketPaint,
    );
    canvas.drawLine(
      Offset(left + scanAreaSize, top),
      Offset(left + scanAreaSize, top + bracketLength),
      bracketPaint,
    );

    // Bottom-left
    canvas.drawLine(
      Offset(left, top + scanAreaSize - bracketLength),
      Offset(left, top + scanAreaSize),
      bracketPaint,
    );
    canvas.drawLine(
      Offset(left, top + scanAreaSize),
      Offset(left + bracketLength, top + scanAreaSize),
      bracketPaint,
    );

    // Bottom-right
    canvas.drawLine(
      Offset(left + scanAreaSize - bracketLength, top + scanAreaSize),
      Offset(left + scanAreaSize, top + scanAreaSize),
      bracketPaint,
    );
    canvas.drawLine(
      Offset(left + scanAreaSize, top + scanAreaSize - bracketLength),
      Offset(left + scanAreaSize, top + scanAreaSize),
      bracketPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
