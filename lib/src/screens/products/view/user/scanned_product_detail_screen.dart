import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/scanned_product_model.dart';
import 'package:luster/src/screens/products/providers/products_providers.dart';
import 'package:luster/src/screens/products/view/admin/widgets/plan_entry_card.widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

/// Scanned product detail screen with progress tracking
class ScannedProductDetailScreen extends HookConsumerWidget {
  final ScannedProductModel scannedProduct;

  const ScannedProductDetailScreen({
    super.key,
    required this.scannedProduct,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final product = scannedProduct.product;
    final plan = scannedProduct.plan;
    final currentDay = scannedProduct.calculateCurrentDay();
    final completedEntries = useState(scannedProduct.completedEntries ?? {});

    return Scaffold(
      backgroundColor: ColorManager.backgroundColor,
      appBar: BaseAppBar(
        title: context.tr.productDetails,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Header Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppSpaces.padding20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    ColorManager.primaryColor,
                    ColorManager.primaryColor.withOpacity(0.8),
                  ],
                ),
              ),
              child: Column(
                children: [
                  // Product Image
                  if (product?.primaryImage != null)
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(AppRadius.radius12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            spreadRadius: 0,
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(AppRadius.radius12),
                        child: CachedNetworkImage(
                          imageUrl: product!.primaryImage!,
                          width: 150.w,
                          height: 150.h,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  AppGaps.gap16,
                  // Product Name
                  Text(
                    product?.nameByLang(context) ?? context.tr.unknownProduct,
                    style: AppTextStyles.title.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (product?.descriptionByLang(context) != null) ...[
                    AppGaps.gap8,
                    Text(
                      product!.descriptionByLang(context)!,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  AppGaps.gap16,
                  // Progress Card
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem(
                          context,
                          Icons.calendar_today_rounded,
                          '${context.tr.currentDay}',
                          '$currentDay',
                        ),
                        Container(
                          width: 1,
                          height: 40.h,
                          color: Colors.white.withOpacity(0.3),
                        ),
                        _buildStatItem(
                          context,
                          Icons.check_circle_rounded,
                          context.tr.progress,
                          '${(scannedProduct.progressPercentage * 100).toInt()}%',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Plan Progress Section
            if (plan != null && plan.entries.isNotEmpty) ...[
              Padding(
                padding: EdgeInsets.all(16.w),
                child: PlanProgressTable(
                  entries: plan.entries,
                  currentDay: currentDay,
                  completedEntries: completedEntries.value,
                  onToggleComplete: (day, time, completed) async {
                    final key = '${day}_$time';
                    final updated =
                        Map<String, bool>.from(completedEntries.value);
                    updated[key] = completed;
                    completedEntries.value = updated;

                    // Save to GetStorage
                    await ref
                        .read(scannedProductsProvider.notifier)
                        .updateProgress(
                          scannedProduct.productDocumentId,
                          updated,
                        );
                  },
                ),
              ),
            ] else
              Padding(
                padding: EdgeInsets.all(32.w),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.event_busy_rounded,
                        size: 64.sp,
                        color: ColorManager.greyIcon,
                      ),
                      AppGaps.gap16,
                      Text(
                        context.tr.noPlanAvailable,
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: ColorManager.greyText,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Buy Button
            if (product != null)
              Padding(
                padding: EdgeInsets.all(16.w),
                child: SizedBox(
                  width: double.infinity,
                  height: 50.h,
                  child: Button(
                    label: context.tr.buyNow,
                    onPressed: () => _launchBuyUrl(product.buyUrl),
                    color: ColorManager.primaryColor,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      BuildContext context, IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 24.sp,
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          value,
          style: AppTextStyles.headlineSmall.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Future<void> _launchBuyUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
