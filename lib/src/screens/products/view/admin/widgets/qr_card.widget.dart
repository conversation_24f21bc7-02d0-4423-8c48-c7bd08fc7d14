import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:share_plus/share_plus.dart';
import 'package:xr_helper/xr_helper.dart';

/// Beautiful QR code card with save and share functionality
class QRCard extends StatefulWidget {
  final String data;
  final String? productName;
  final bool showActions;

  const QRCard({
    super.key,
    required this.data,
    this.productName,
    this.showActions = true,
  });

  @override
  State<QRCard> createState() => _QRCardState();
}

class _QRCardState extends State<QRCard> {
  final GlobalKey _qrKey = GlobalKey();
  bool _isSaving = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppRadius.radius16),
        boxShadow: [
          BoxShadow(
            color: ColorManager.primaryColor.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Product Name Header
          if (widget.productName != null) ...[
            Text(
              widget.productName!,
              style: AppTextStyles.title.copyWith(
                color: ColorManager.primaryColor,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            AppGaps.gap16,
          ],

          // QR Code with RepaintBoundary for image capture
          RepaintBoundary(
            key: _qrKey,
            child: Container(
              padding: const EdgeInsets.all(AppSpaces.padding16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppRadius.radius12),
              ),
              child: PrettyQrView.data(
                data: widget.data,
                decoration: PrettyQrDecoration(
                  shape: PrettyQrSmoothSymbol(
                    color: ColorManager.primaryColor,
                  ),
                  image: PrettyQrDecorationImage(
                    image: const AssetImage('assets/images/logo_symbol.png'),
                    scale: 0.2,
                  ),
                ),
              ),
            ),
          ),

          // Action Buttons
          if (widget.showActions) ...[
            AppGaps.gap16,
            Row(
              children: [
                // Save Button
                Expanded(
                  child: Button(
                    label: context.tr.saveQR,
                    onPressed: _isSaving ? null : _saveQRCode,
                    isLoading: _isSaving,
                    color: ColorManager.primaryColor,
                  ),
                ),
                AppGaps.gap12,
                // Share Button
                Expanded(
                  child: Button(
                    label: context.tr.shareQR,
                    onPressed: _shareQRCode,
                    color: ColorManager.primaryColor.withOpacity(0.1),
                    isWhiteText: false,
                    haveElevation: false,
                    isOutLine: true,
                    textColor: ColorManager.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _saveQRCode() async {
    setState(() => _isSaving = true);

    try {
      // Capture the QR code as image
      final boundary =
          _qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();

      // Save to gallery
      final result = await ImageGallerySaverPlus.saveImage(
        Uint8List.fromList(pngBytes),
        quality: 100,
        name: 'luster_qr_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (result['isSuccess'] == true && mounted) {
        showToast(context.tr.qrSavedSuccessfully);
      } else if (mounted) {
        showToast(context.tr.qrSaveFailed, isError: true);
      }
    } catch (e) {
      if (mounted) {
        showToast(context.tr.qrSaveFailed, isError: true);
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  Future<void> _shareQRCode() async {
    try {
      // Capture the QR code as image
      final boundary =
          _qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();

      // Save to temporary directory
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/qr_code.png').create();

      await file.writeAsBytes(pngBytes);

      // Share the file
      final box = context.findRenderObject() as RenderBox?;
      await Share.shareXFiles(
        [XFile(file.path)],
        text: widget.productName != null
            ? '${widget.productName} - Luster Reminder'
            : 'Luster Reminder QR Code',
        sharePositionOrigin: box != null && Platform.isIOS
            ? box.localToGlobal(Offset.zero) & box.size
            : null,
      );
    } catch (e, s) {
      log('asafasfsaf $e $s');
      if (mounted) {
        showToast(context.tr.qrSaveFailed, isError: true);
      }
    }
  }
}
