import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/plan_entry_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../auth/models/user_model.dart';

/// Beautiful plan entry card showing day, time, and action
class PlanEntryCard extends StatelessWidget {
  final PlanEntryModel entry;
  final bool isCompleted;
  final bool isCurrentDay;
  final bool isEditable;
  final VoidCallback? onToggleComplete;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const PlanEntryCard({
    super.key,
    required this.entry,
    this.isCompleted = false,
    this.isCurrentDay = false,
    this.isEditable = false,
    this.onToggleComplete,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: const EdgeInsets.all(AppSpaces.padding12),
      decoration: BoxDecoration(
        color: isCurrentDay
            ? ColorManager.primaryColor.withOpacity(0.05)
            : ColorManager.primaryOpacityContainer,
        borderRadius: BorderRadius.circular(AppRadius.radius12),
        border: Border.all(
          color: isCurrentDay
              ? ColorManager.primaryColor.withOpacity(0.3)
              : ColorManager.primaryColor.withOpacity(0.1),
          width: isCurrentDay ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // Completion Checkbox
          if (onToggleComplete != null)
            GestureDetector(
              onTap: onToggleComplete,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 24.w,
                height: 24.h,
                decoration: BoxDecoration(
                  color: isCompleted ? ColorManager.primaryColor : Colors.white,
                  borderRadius: BorderRadius.circular(6.r),
                  border: Border.all(
                    color: ColorManager.primaryColor,
                    width: 2,
                  ),
                ),
                child: isCompleted
                    ? Icon(
                        Icons.check,
                        size: 16.sp,
                        color: Colors.white,
                      )
                    : null,
              ),
            ),

          if (onToggleComplete != null) SizedBox(width: 12.w),

          // Day Badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 6.h,
            ),
            decoration: BoxDecoration(
              color: isCurrentDay
                  ? ColorManager.primaryColor
                  : ColorManager.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              '${context.tr.day} ${entry.day}',
              style: AppTextStyles.labelSmall.copyWith(
                color: isCurrentDay ? Colors.white : ColorManager.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          SizedBox(width: 12.w),

          // Time Badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 6.h,
            ),
            decoration: BoxDecoration(
              color: ColorManager.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.access_time_rounded,
                  size: 14.sp,
                  color: ColorManager.primaryColor,
                ),
                SizedBox(width: 4.w),
                Text(
                  entry.time,
                  style: AppTextStyles.labelSmall.copyWith(
                    color: ColorManager.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(width: 12.w),

          // Action Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  entry.key,
                  style: AppTextStyles.labelMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: ColorManager.primaryColor,
                    decoration: isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
                if (entry.value.isNotEmpty) ...[
                  SizedBox(height: 2.h),
                  Text(
                    entry.value,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: ColorManager.greyText,
                      decoration:
                          isCompleted ? TextDecoration.lineThrough : null,
                    ),
                  ),
                ],
                if (entry.description != null &&
                    entry.description!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  Text(
                    entry.description!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: ColorManager.greyText.withOpacity(0.7),
                      fontStyle: FontStyle.italic,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),

          // Edit/Delete Buttons (if editable)
          if (isEditable) ...[
            SizedBox(width: 8.w),
            if (onEdit != null)
              GestureDetector(
                onTap: onEdit,
                child: Icon(
                  Icons.edit_rounded,
                  size: 18.sp,
                  color: ColorManager.primaryColor,
                ),
              ),
            SizedBox(width: 8.w),
            if (onDelete != null)
              GestureDetector(
                onTap: onDelete,
                child: Icon(
                  Icons.delete_rounded,
                  size: 18.sp,
                  color: ColorManager.errorColor,
                ),
              ),
          ],
        ],
      ),
    );
  }
}

/// Progress table showing daily plan entries
class PlanProgressTable extends StatelessWidget {
  final List<PlanEntryModel> entries;
  final int currentDay;
  final Map<String, bool>? completedEntries;
  final Function(int day, String time, bool completed)? onToggleComplete;

  const PlanProgressTable({
    super.key,
    required this.entries,
    required this.currentDay,
    this.completedEntries,
    this.onToggleComplete,
  });

  @override
  Widget build(BuildContext context) {
    // Group entries by day
    final Map<int, List<PlanEntryModel>> entriesByDay = {};
    for (var entry in entries) {
      if (!entriesByDay.containsKey(entry.day)) {
        entriesByDay[entry.day] = [];
      }
      entriesByDay[entry.day]!.add(entry);
    }

    // Sort days
    final sortedDays = entriesByDay.keys.toList()..sort();

    // Sort entries within each day by time
    for (var day in sortedDays) {
      entriesByDay[day]!.sort((a, b) => a.time.compareTo(b.time));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(
              Icons.calendar_today_rounded,
              size: 20.sp,
              color: ColorManager.primaryColor,
            ),
            SizedBox(width: 8.w),
            Text(
              context.tr.usagePlan,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
                color: ColorManager.primaryColor,
              ),
            ),
            const Spacer(),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 6.h,
              ),
              decoration: BoxDecoration(
                color: ColorManager.primaryColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                '${context.tr.currentDay}: $currentDay',
                style: AppTextStyles.labelSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),

        AppGaps.gap16,

        // Entries grouped by day
        ...sortedDays.map((day) {
          final dayEntries = entriesByDay[day]!;
          final isCurrentDay = day == currentDay;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Day entries (already sorted by time)
              ...dayEntries.map((entry) {
                final entryKey = '${entry.day}_${entry.time}';
                final isCompleted = completedEntries?[entryKey] ?? false;

                return PlanEntryCard(
                  entry: entry,
                  isCompleted: isCompleted,
                  isCurrentDay: isCurrentDay,
                  onToggleComplete: onToggleComplete != null
                      ? () =>
                          onToggleComplete!(entry.day, entry.time, !isCompleted)
                      : null,
                );
              }),
            ],
          );
        }),
      ],
    );
  }
}
