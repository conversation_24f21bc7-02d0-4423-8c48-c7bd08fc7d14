import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/products/models/plan_entry_model.dart';
import 'package:xr_helper/xr_helper.dart';

/// Dialog for adding/editing plan entries
class PlanEntryDialog extends StatefulWidget {
  final PlanEntryModel? initialEntry;
  final List<PlanEntryModel> existingEntries;

  const PlanEntryDialog({
    super.key,
    this.initialEntry,
    required this.existingEntries,
  });

  @override
  State<PlanEntryDialog> createState() => _PlanEntryDialogState();
}

class _PlanEntryDialogState extends State<PlanEntryDialog> {
  late TextEditingController keyController;
  late TextEditingController valueController;
  late TextEditingController descriptionController;
  late int selectedDay;
  late String selectedTime;

  /// Calculate available days based on existing entries
  /// Days must be sequential: 1, 2, 3, etc.
  List<int> _getAvailableDays() {
    if (widget.existingEntries.isEmpty) {
      return [1]; // Only day 1 if no entries
    }

    // Get all unique days from existing entries
    final existingDays =
        widget.existingEntries.map((e) => e.day).toSet().toList();
    existingDays.sort();

    // Find the maximum day
    final maxDay = existingDays.isNotEmpty ? existingDays.last : 1;

    // Generate sequential days from 1 to maxDay + 1
    return List.generate(maxDay + 1, (index) => index + 1);
  }

  @override
  void initState() {
    super.initState();
    keyController = TextEditingController(text: widget.initialEntry?.key ?? '');
    valueController =
        TextEditingController(text: widget.initialEntry?.value ?? '');
    descriptionController =
        TextEditingController(text: widget.initialEntry?.description ?? '');

    // Set initial day
    final availableDays = _getAvailableDays();
    if (widget.initialEntry != null) {
      // When editing, use the entry's day
      selectedDay = widget.initialEntry!.day;
    } else {
      // When adding, default to the last available day
      selectedDay = availableDays.last;
    }

    selectedTime = widget.initialEntry?.time ?? '09:00';
  }

  @override
  void dispose() {
    keyController.dispose();
    valueController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: int.parse(selectedTime.split(':')[0]),
        minute: int.parse(selectedTime.split(':')[1]),
      ),
    );
    if (picked != null) {
      setState(() {
        selectedTime =
            '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppRadius.radius16),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Text(
                widget.initialEntry == null
                    ? context.tr.addNewEntry
                    : context.tr.editEntry,
                style: AppTextStyles.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: ColorManager.primaryColor,
                ),
              ),
              AppGaps.gap16,

              // Day Selector
              Text(
                '${context.tr.day}:',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.gap8,
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w),
                decoration: BoxDecoration(
                  border: Border.all(color: ColorManager.primaryColor),
                  borderRadius: BorderRadius.circular(AppRadius.radius8),
                ),
                child: DropdownButton<int>(
                  value: selectedDay,
                  isExpanded: true,
                  underline: const SizedBox(),
                  items: _getAvailableDays().map((day) {
                    return DropdownMenuItem(
                      value: day,
                      child: Text('${context.tr.day} $day'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => selectedDay = value);
                    }
                  },
                ),
              ),
              AppGaps.gap16,

              // Time Picker
              Text(
                '${context.tr.time}:',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.gap8,
              GestureDetector(
                onTap: _selectTime,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 12.h,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: ColorManager.primaryColor),
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time_rounded,
                        color: ColorManager.primaryColor,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        selectedTime,
                        style: AppTextStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              AppGaps.gap16,

              // Key/Action Field
              Text(
                '${context.tr.action}:',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.gap8,
              TextField(
                controller: keyController,
                decoration: InputDecoration(
                  hintText: context.tr.enterAction,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                ),
              ),
              AppGaps.gap16,

              // Value/Description Field
              Text(
                '${context.tr.description}:',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.gap8,
              TextField(
                controller: valueController,
                decoration: InputDecoration(
                  hintText: context.tr.enterDescription,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                ),
              ),
              AppGaps.gap16,

              // Additional Notes Field
              Text(
                '${context.tr.notes}:',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.gap8,
              TextField(
                controller: descriptionController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: context.tr.enterNotes,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                ),
              ),
              AppGaps.gap24,

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      child: Text(
                        context.tr.cancel,
                        style: AppTextStyles.labelMedium,
                      ),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Button(
                      label: context.tr.save,
                      onPressed: () {
                        if (keyController.text.isEmpty) {
                          showToast(context.tr.pleaseEnterAction,
                              isError: true);
                          return;
                        }

                        final entry = PlanEntryModel(
                          day: selectedDay,
                          time: selectedTime,
                          key: keyController.text,
                          value: valueController.text,
                          description: descriptionController.text.isEmpty
                              ? null
                              : descriptionController.text,
                        );

                        Navigator.pop(context, entry);
                      },
                      color: ColorManager.primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
