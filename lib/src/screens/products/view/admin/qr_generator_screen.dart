import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/shared/widgets/app_bar/base_appbar.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:luster/src/screens/auth/models/user_model.dart';
import 'package:luster/src/screens/products/models/plan_entry_model.dart';
import 'package:luster/src/screens/products/models/plan_model.dart';
import 'package:luster/src/screens/products/models/product_model.dart';
import 'package:luster/src/screens/products/providers/products_providers.dart';
import 'package:luster/src/screens/products/view/admin/widgets/plan_entry_card.widget.dart';
import 'package:luster/src/screens/products/view/admin/widgets/plan_entry_dialog.widget.dart';
import 'package:luster/src/screens/products/view/admin/widgets/qr_card.widget.dart';
import 'package:xr_helper/xr_helper.dart';

/// Admin screen to create plan and generate QR code
class QRGeneratorScreen extends HookConsumerWidget {
  final ProductModel product;

  const QRGeneratorScreen({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Load existing plan from Supabase
    final planAsync =
        ref.watch(getPlanByProductIdProvider(product.documentId!));

    final planEntries = useState<List<PlanEntryModel>>([]);
    final showQR = useState(false);
    final isSaving = useState(false);
    final isLoaded = useState(false);

    // Initialize with existing plan data
    useEffect(() {
      planAsync.whenData((plan) {
        if (plan != null && !isLoaded.value) {
          planEntries.value = plan.entries;
          isLoaded.value = true;
        }
      });
      return null;
    }, [planAsync]);

    Future<void> savePlan() async {
      if (product.documentId == null) {
        showToast(context.tr.errorOccurred, isError: true);
        return;
      }

      isSaving.value = true;
      try {
        final plan = PlanModel(
          productDocumentId: product.documentId!,
          planIndex: 1, // Default plan index
          entries: planEntries.value,
          createdByAdmin: 'admin', // TODO: Get from auth context
        );

        await ref.read(plansControllerProvider).createPlan(plan);

        // Invalidate to refresh the plan
        ref.invalidate(getPlanByProductIdProvider(product.documentId!));

        showQR.value = true;
        showToast(context.tr.planSavedSuccessfully);
      } catch (e) {
        showToast('${context.tr.errorOccurred}: $e', isError: true);
      } finally {
        isSaving.value = false;
      }
    }

    /// Sort plan entries by day first, then by time
    List<PlanEntryModel> _getSortedPlanEntries() {
      final sorted = [...planEntries.value];
      sorted.sort((a, b) {
        // First sort by day
        final dayComparison = a.day.compareTo(b.day);
        if (dayComparison != 0) {
          return dayComparison;
        }
        // If same day, sort by time
        return a.time.compareTo(b.time);
      });
      return sorted;
    }

    return Scaffold(
      backgroundColor: ColorManager.backgroundColor,
      appBar: BaseAppBar(
        title: context.tr.generateQR,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Info Card
            Container(
              padding: const EdgeInsets.all(AppSpaces.padding16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppRadius.radius12),
                boxShadow: [
                  BoxShadow(
                    color: ColorManager.primaryColor.withOpacity(0.08),
                    spreadRadius: 0,
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Product Image
                  if (product.primaryImage != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(AppRadius.radius8),
                      child: CachedNetworkImage(
                        imageUrl: product.primaryImage!,
                        width: 80.w,
                        height: 80.h,
                        fit: BoxFit.cover,
                      ),
                    ),
                  SizedBox(width: 16.w),
                  // Product Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.nameByLang(context),
                          style: AppTextStyles.headlineSmall.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                        if (product.descriptionByLang(context) != null) ...[
                          SizedBox(height: 4.h),
                          Text(
                            product.descriptionByLang(context)?.trim() ?? '',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.greyText,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (product.actualPrice > 0) ...[
                    AppGaps.gap8,
                    Column(
                      children: [
                        Text(
                          '${product.actualPrice.toStringAsFixed(2)} EGP',
                          style: AppTextStyles.labelMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: product.isSale
                                ? ColorManager.errorColor
                                : ColorManager.primaryColor,
                          ),
                        ),
                        if (product.isSale && product.totalPrice != null) ...[
                          Text(
                            '${product.totalPrice!.toStringAsFixed(2)} EGP',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: ColorManager.greyText,
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ],
              ),
            ),

            AppGaps.gap24,

            // Plan Section
            Row(
              children: [
                Icon(
                  Icons.event_note_rounded,
                  size: 24.sp,
                  color: ColorManager.primaryColor,
                ),
                SizedBox(width: 8.w),
                Text(
                  context.tr.usagePlan,
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: ColorManager.primaryColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.add_circle_rounded),
                  color: ColorManager.primaryColor,
                  onPressed: () async {
                    // Show dialog to add new entry
                    final result = await showDialog<PlanEntryModel>(
                      context: context,
                      builder: (context) => PlanEntryDialog(
                        existingEntries: planEntries.value,
                      ),
                    );

                    if (result != null) {
                      planEntries.value = [
                        ...planEntries.value,
                        result,
                      ];
                    }
                  },
                ),
              ],
            ),

            AppGaps.gap12,

            // Loading state
            if (planAsync.isLoading)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(32.w),
                  child: const CircularProgressIndicator(),
                ),
              )
            // Plan Entries
            else if (planEntries.value.isEmpty)
              Container(
                padding: EdgeInsets.all(32.w),
                decoration: BoxDecoration(
                  color: ColorManager.primaryOpacityContainer,
                  borderRadius: BorderRadius.circular(AppRadius.radius12),
                  border: Border.all(
                    color: ColorManager.primaryColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.event_busy_rounded,
                        size: 48.sp,
                        color: ColorManager.greyIcon,
                      ),
                      AppGaps.gap8,
                      Text(
                        context.tr.noPlanAvailable,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: ColorManager.greyText,
                        ),
                      ),
                      AppGaps.gap8,
                      Text(
                        context.tr.tapToAddEntries,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: ColorManager.greyText,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ..._getSortedPlanEntries().asMap().entries.map((e) {
                final index = e.key;
                final entry = e.value;
                // Find the original index in planEntries.value for edit/delete operations
                final originalIndex = planEntries.value.indexOf(entry);

                return PlanEntryCard(
                  entry: entry,
                  isEditable: true,
                  isCurrentDay: false,
                  onEdit: () async {
                    final result = await showDialog<PlanEntryModel>(
                      context: context,
                      builder: (context) => PlanEntryDialog(
                        initialEntry: entry,
                        existingEntries: planEntries.value,
                      ),
                    );

                    if (result != null) {
                      final newEntries = [...planEntries.value];
                      newEntries[originalIndex] = result;
                      planEntries.value = newEntries;
                    }
                  },
                  onDelete: () {
                    final newEntries = [...planEntries.value];
                    newEntries.removeAt(originalIndex);
                    planEntries.value = newEntries;
                  },
                );
              }),

            AppGaps.gap24,

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: Button(
                    label: context.tr.savePlan,
                    onPressed: (isSaving.value || planEntries.value.isEmpty)
                        ? null
                        : savePlan,
                    isLoading: isSaving.value,
                    color: ColorManager.primaryColor,
                  ),
                ),
                if (showQR.value) ...[
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Button(
                      label: context.tr.generateQR,
                      onPressed: () {
                        showQR.value = !showQR.value;
                      },
                      color: ColorManager.primaryColor.withOpacity(0.1),
                      isWhiteText: false,
                      textColor: ColorManager.primaryColor,
                    ),
                  ),
                ],
              ],
            ),

            // QR Code Display
            if (showQR.value) ...[
              AppGaps.gap24,
              QRCard(
                data: product.documentId ?? '',
                productName: product.nameByLang(context),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
