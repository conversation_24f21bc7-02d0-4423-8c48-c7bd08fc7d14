// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:luster/src/core/shared/extensions/context_extensions.dart';
// import 'package:luster/src/core/shared/extensions/riverpod_extensions.dart';
// import 'package:luster/src/core/shared/widgets/app_bar/base_appbar.dart';
// import 'package:luster/src/core/shared/widgets/empty_widget/empty_widget.dart';
// import 'package:luster/src/core/theme/color_manager.dart';
// import 'package:luster/src/screens/products/models/category_model.dart';
// import 'package:luster/src/screens/products/models/product_model.dart';
// import 'package:luster/src/screens/products/providers/products_providers.dart';
// import 'package:luster/src/screens/products/view/admin/qr_generator_screen.dart';
// import 'package:luster/src/screens/products/view/user/home/<USER>/category_chip.widget.dart';
// import 'package:luster/src/screens/products/view/user/home/<USER>/product_card.widget.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// /// Admin screen to view products and generate QR codes
// class AdminProductsScreen extends HookConsumerWidget {
//   const AdminProductsScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final categoriesAsync = ref.watch(getCategoriesFutureProvider);
//     final productsAsync = ref.watch(getProductsFutureProvider);
//     final selectedCategory = useState<CategoryModel?>(null);
//
//     // Filter products by selected category
//     final filteredProducts = useMemoized(() {
//       return productsAsync.when(
//         data: (products) {
//           if (selectedCategory.value == null) return products;
//           return products
//               .where((p) =>
//                   p.category?.documentId == selectedCategory.value?.documentId)
//               .toList();
//         },
//         loading: () => <ProductModel>[],
//         error: (_, __) => <ProductModel>[],
//       );
//     }, [productsAsync, selectedCategory.value]);
//
//     return Scaffold(
//       backgroundColor: ColorManager.backgroundColor,
//       appBar: BaseAppBar(
//         title: context.tr.products,
//         actions: [
//           // Refresh Icon
//           IconButton(
//             icon: const Icon(Icons.refresh_rounded),
//             onPressed: () {
//               ref.invalidate(getCategoriesFutureProvider);
//               ref.invalidate(getProductsFutureProvider);
//             },
//           ),
//         ],
//       ),
//       body: RefreshIndicator(
//         onRefresh: () async {
//           ref.invalidate(getCategoriesFutureProvider);
//           ref.invalidate(getProductsFutureProvider);
//           await ref.read(getCategoriesFutureProvider.future);
//           await ref.read(getProductsFutureProvider.future);
//         },
//         child: Column(
//           children: [
//             // Categories Filter
//             categoriesAsync.when(
//               data: (categories) {
//                 if (categories.isEmpty) return const SizedBox.shrink();
//                 return Column(
//                   children: [
//                     Container(
//                       height: 50.h,
//                       padding: EdgeInsets.symmetric(vertical: 8.h),
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         boxShadow: [
//                           BoxShadow(
//                             color: ColorManager.primaryColor.withOpacity(0.05),
//                             spreadRadius: 0,
//                             blurRadius: 8,
//                             offset: const Offset(0, 2),
//                           ),
//                         ],
//                       ),
//                       child: ListView.separated(
//                         scrollDirection: Axis.horizontal,
//                         padding: EdgeInsets.symmetric(horizontal: 16.w),
//                         itemCount: categories.length + 1,
//                         separatorBuilder: (context, index) =>
//                             SizedBox(width: 8.w),
//                         itemBuilder: (context, index) {
//                           if (index == 0) {
//                             // "All" category
//                             return CategoryChip(
//                               category: CategoryModel(
//                                 documentId: 'all',
//                                 name: context.tr.viewAll,
//                               ),
//                               isSelected: selectedCategory.value == null,
//                               onTap: () => selectedCategory.value = null,
//                             );
//                           }
//
//                           final category = categories[index - 1];
//                           return CategoryChip(
//                             category: category,
//                             isSelected: selectedCategory.value?.documentId ==
//                                 category.documentId,
//                             onTap: () => selectedCategory.value = category,
//                           );
//                         },
//                       ),
//                     ),
//                     AppGaps.gap8,
//                   ],
//                 );
//               },
//               loading: () => const SizedBox.shrink(),
//               error: (_, __) => const SizedBox.shrink(),
//             ),
//
//             // Products Grid
//             Expanded(
//               child: productsAsync.get<Widget>(
//                 data: (_) {
//                   if (filteredProducts.isEmpty) {
//                     return const EmptyDataWidget();
//                   }
//                   return _buildProductsGrid(context, filteredProducts);
//                 },
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
//
//   Widget _buildProductsGrid(BuildContext context, List<ProductModel> products) {
//     return GridView.builder(
//       padding: EdgeInsets.all(16.w),
//       gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//         crossAxisCount: 2,
//         crossAxisSpacing: 12.w,
//         mainAxisSpacing: 12.h,
//         childAspectRatio: 0.75,
//       ),
//       itemCount: products.length,
//       itemBuilder: (context, index) {
//         final product = products[index];
//         return ProductCard(
//           product: product,
//           onTap: () {
//             // Navigate to QR generator
//             Navigator.push(
//               context,
//               MaterialPageRoute(
//                 builder: (context) => QRGeneratorScreen(product: product),
//               ),
//             );
//           },
//         );
//       },
//     );
//   }
// }
