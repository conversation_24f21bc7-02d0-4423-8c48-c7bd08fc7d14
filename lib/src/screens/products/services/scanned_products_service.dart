import 'package:get_storage/get_storage.dart';
import 'package:luster/src/screens/products/models/scanned_product_model.dart';

/// Service for managing scanned products using GetStorage
class ScannedProductsService {
  static const String _storageKey = 'scanned_products';
  final GetStorage _storage;

  ScannedProductsService(this._storage);

  /// Get all scanned products
  List<ScannedProductModel> getAllScannedProducts() {
    try {
      final data = _storage.read<List>(_storageKey);
      if (data == null) return [];

      return data
          .map((json) => ScannedProductModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Get scanned product by product documentId
  ScannedProductModel? getScannedProduct(String productDocumentId) {
    try {
      final products = getAllScannedProducts();
      return products.firstWhere(
        (p) => p.productDocumentId == productDocumentId,
        orElse: () => throw Exception('Not found'),
      );
    } catch (e) {
      return null;
    }
  }

  /// Save a scanned product
  Future<void> saveScannedProduct(ScannedProductModel scannedProduct) async {
    try {
      final products = getAllScannedProducts();

      // Remove existing product with same documentId
      products.removeWhere((p) => p.productDocumentId == scannedProduct.productDocumentId);

      // Add new product
      products.add(scannedProduct);

      // Save to storage
      await _storage.write(_storageKey, products.map((p) => p.toJson()).toList());
    } catch (e) {
      rethrow;
    }
  }

  /// Update scanned product progress
  Future<void> updateProgress(
    String productDocumentId,
    Map<String, bool> completedEntries,
  ) async {
    try {
      final products = getAllScannedProducts();
      final index = products.indexWhere((p) => p.productDocumentId == productDocumentId);

      if (index == -1) return;

      // Update completed entries
      products[index] = ScannedProductModel(
        productDocumentId: products[index].productDocumentId,
        product: products[index].product,
        plan: products[index].plan,
        scannedAt: products[index].scannedAt,
        completedEntries: completedEntries,
        currentDayIndex: products[index].currentDayIndex,
      );

      // Save to storage
      await _storage.write(_storageKey, products.map((p) => p.toJson()).toList());
    } catch (e) {
      rethrow;
    }
  }

  /// Delete a scanned product
  Future<void> deleteScannedProduct(String productDocumentId) async {
    try {
      final products = getAllScannedProducts();
      products.removeWhere((p) => p.productDocumentId == productDocumentId);

      await _storage.write(_storageKey, products.map((p) => p.toJson()).toList());
    } catch (e) {
      rethrow;
    }
  }

  /// Clear all scanned products
  Future<void> clearAll() async {
    await _storage.remove(_storageKey);
  }
}

