import 'dart:developer';

import 'package:luster/src/screens/auth/models/user_model.dart';
import 'package:luster/src/screens/splash/view/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:xr_helper/xr_helper.dart';

import 'core/consts/app_constants.dart';
import 'core/shared/services/app_settings/controller/settings_controller.dart';
import 'core/shared/widgets/loading/loading_widget.dart';
import 'core/theme/theme_manager.dart';

class BaseApp extends HookConsumerWidget {
  const BaseApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);

    ScreenUtil.init(context);

    final theme = AppTheme(
        appTextTheme: settingsController.isEnglish
            ? GoogleFonts.workSansTextTheme()
            : GoogleFonts.cairoTextTheme());

    return BaseMaterialApp(
      title: AppConsts.appName,
      //? Localization
      locale: settingsController.locale,
      supportedLocales: AppConsts.supportedLocales,
      localizationsDelegates: AppConsts.localizationsDelegates,
      //? Theme
      theme: theme.appTheme(),
      loadingWidget: const LoadingWidget(),
      home:
          // const MainScreen(),
          const SplashScreen(),
      // const SplashScreen()
      // const MainScreen(),
    );
  }
}
