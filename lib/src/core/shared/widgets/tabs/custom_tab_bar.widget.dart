import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../consts/app_constants.dart';

class CustomTabBarWidget extends HookWidget {
  final List<String> tabTitles;
  final List<Widget> children;
  final ValueChanged<int>? onTabChanged;
  final int? initialIndex;
  final bool isScrollable;
  final Color? color;

  const CustomTabBarWidget({
    super.key,
    required this.tabTitles,
    required this.children,
    this.onTabChanged,
    this.color,
    this.isScrollable = false,
    this.initialIndex = 0,
  }) : assert(tabTitles.length == children.length,
            'Tab titles and children must have the same length');

  @override
  Widget build(BuildContext context) {
    final tabController = useTabController(
      initialLength: tabTitles.length,
      initialIndex: initialIndex ?? 0,
    );

    final selectedIndex = useState<int>(initialIndex ?? 0);

    // Listen to tab controller changes
    useEffect(() {
      void listener() {
        if (tabController.index != selectedIndex.value) {
          selectedIndex.value = tabController.index;
          onTabChanged?.call(tabController.index);
        }
      }

      tabController.addListener(listener);
      return () => tabController.removeListener(listener);
    }, [tabController]);

    Widget buildTab(String text, {bool isSelected = false}) {
      return Tab(
        child: Container(
          decoration: isSelected
              ? null
              : BoxDecoration(
                  border: Border.all(
                    color: color ?? ColorManager.primaryColor,
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(7.r),
                ),
          alignment: Alignment.center,
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Padding(
              padding: const EdgeInsets.all(6.0),
              child: Text(
                text,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        TabBar(
          isScrollable: isScrollable,
          tabAlignment: isScrollable ? TabAlignment.start : null,
          controller: tabController,
          labelPadding:
              EdgeInsets.symmetric(horizontal: isScrollable ? 6.w : 8.w),
          indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(7.r),
            color: color ?? ColorManager.primaryColor,
          ),
          indicatorPadding:
              EdgeInsets.symmetric(horizontal: isScrollable ? 4.w : 8.w),
          indicatorSize: TabBarIndicatorSize.tab,
          labelColor: Colors.white,
          unselectedLabelColor: color ?? ColorManager.primaryColor,
          labelStyle: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            fontFamily: AppConsts.fontFamily,
          ),
          unselectedLabelStyle: AppTextStyles.bodyMedium.copyWith(
            fontFamily: AppConsts.fontFamily,
          ),
          dividerColor: Colors.transparent,
          tabs: tabTitles.asMap().entries.map((entry) {
            final index = entry.key;
            final title = entry.value;
            return buildTab(
              title,
              isSelected: selectedIndex.value == index,
            );
          }).toList(),
        ),

        AppGaps.gap16,

        // Tab Bar View
        Expanded(
          child: TabBarView(
            controller: tabController,
            children: children,
          ),
        ),
      ],
    );
  }
}
