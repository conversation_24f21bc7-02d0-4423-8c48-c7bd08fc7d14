import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';

class BaseTextField extends StatefulWidget {
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final TextInputType textInputType;
  final Function(dynamic)? onChanged;
  final TextAlign textAlign;
  final Function()? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? icon;
  final Widget? suffixIcon;
  final Widget? titleIcon;
  final String? label;
  final String name;
  final String? hint;
  final int maxLines;
  final String? ignoringMessage;
  final String? Function(String?)? validator;
  final bool isObscure;
  final bool isRequired;
  final bool withoutEnter;
  final String? initialValue;
  final String? title;
  final bool? enabled;
  final bool useUnderlineBorder;
  final String? Function(String?)? realTimeValidator;

  const BaseTextField({
    super.key,
    this.ignoringMessage,
    required this.name,
    this.enabled = true,
    this.focusNode,
    this.controller,
    this.isObscure = false,
    this.withoutEnter = false,
    this.onTap,
    this.hint,
    this.icon,
    this.suffixIcon,
    this.label,
    this.onChanged,
    this.titleIcon,
    this.initialValue,
    this.textAlign = TextAlign.start,
    this.contentPadding,
    this.textInputType = TextInputType.text,
    this.maxLines = 1,
    this.isRequired = true,
    this.validator,
    this.title,
    this.useUnderlineBorder = true,
    this.realTimeValidator,
  });

  @override
  State<BaseTextField> createState() => _BaseTextFieldState();
}

class _BaseTextFieldState extends State<BaseTextField> {
  late bool _isObscure;
  String? _realTimeError;

  @override
  void initState() {
    super.initState();
    _isObscure = widget.isObscure;
  }

  void _onTextChanged(String? value) {
    if (widget.realTimeValidator != null) {
      setState(() {
        _realTimeError = widget.realTimeValidator!(value);
      });
    }
    if (widget.onChanged != null) {
      widget.onChanged!(value);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.title != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (widget.titleIcon != null) ...[
                widget.titleIcon!,
                AppGaps.gap4,
              ],
              Text(
                widget.title!,
                style: AppTextStyles.labelLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
            ],
          ),
          AppGaps.gap8,
          _buildTextField(context),
          if (_realTimeError != null) ...[
            AppGaps.gap4,
            Text(
              _realTimeError!,
              style: AppTextStyles.labelSmall.copyWith(
                color: Colors.red,
              ),
            ),
          ],
        ],
      );
    }
    return _buildTextField(context);
  }

  Widget _buildTextField(BuildContext context) {
    return FormBuilderTextField(
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.withoutEnter
            ? widget.hint
            : widget.hint ?? widget.title ?? widget.label,
        hintStyle: AppTextStyles.labelMedium.copyWith(
          color: Colors.grey,
          fontSize: 14,
        ),
        labelStyle: AppTextStyles.labelMedium,
        prefixIcon: widget.icon != null
            ? Padding(
                padding: const EdgeInsets.all(8.0),
                child: widget.icon,
              )
            : null,
        suffixIcon: widget.textInputType == TextInputType.visiblePassword
            ? InkWell(
                onTap: () {
                  setState(() {
                    _isObscure = !_isObscure;
                  });
                },
                child: Icon(
                  _isObscure
                      ? Icons.visibility_off_outlined
                      : Icons.visibility_outlined,
                  color: Colors.grey,
                ),
              )
            : widget.suffixIcon != null
                ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: widget.suffixIcon,
                  )
                : null,
        border: widget.useUnderlineBorder
            ? const UnderlineInputBorder(
                borderSide:
                    BorderSide(color: ColorManager.primaryColor, width: 1.0),
              )
            : null,
        enabledBorder: widget.useUnderlineBorder
            ? const UnderlineInputBorder(
                borderSide:
                    BorderSide(color: ColorManager.primaryColor, width: 1.0),
              )
            : null,
        focusedBorder: widget.useUnderlineBorder
            ? const UnderlineInputBorder(
                borderSide:
                    BorderSide(color: ColorManager.primaryColor, width: 2.0),
              )
            : null,
        contentPadding: widget.useUnderlineBorder
            ? const EdgeInsets.only(top: 15, bottom: 10)
            : const EdgeInsets.symmetric(
                horizontal: AppSpaces.padding8,
                vertical: AppSpaces.padding12 + 5,
              ),
      ),
      name: widget.name,
      enableSuggestions: true,
      onTapOutside: (_) {
        FocusScope.of(context).unfocus();
      },
      focusNode: widget.focusNode,
      obscureText: widget.isObscure ? _isObscure : false,
      controller: widget.controller,
      keyboardType: widget.textInputType,
      inputFormatters: [
        if (widget.textInputType == TextInputType.number)
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]')),
      ],
      textAlign: widget.textAlign,
      onChanged: _onTextChanged,
      enabled: widget.enabled ?? true,
      onTap: widget.onTap,
      initialValue: widget.initialValue,
      maxLines: widget.maxLines,
      validator: widget.validator,
      style: AppTextStyles.labelMedium.copyWith(
        fontSize: 16,
        color: Colors.black87,
      ),
    );
  }
}
