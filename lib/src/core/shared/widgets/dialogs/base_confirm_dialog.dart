import 'package:flutter/material.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseConfirmDialog extends StatelessWidget {
  final String title;
  final String message;
  final String? confirmText;
  final String? cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmButtonColor;
  final Color? cancelButtonColor;
  final IconData? icon;
  final Color? iconColor;
  final bool isDestructive;

  const BaseConfirmDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText,
    this.cancelText,
    this.onConfirm,
    this.onCancel,
    this.confirmButtonColor,
    this.cancelButtonColor,
    this.icon,
    this.iconColor,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon (if provided)
            if (icon != null) ...[
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: (iconColor ??
                          (isDestructive
                              ? Colors.red
                              : ColorManager.primaryColor))
                      .withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: iconColor ??
                      (isDestructive ? Colors.red : ColorManager.primaryColor),
                ),
              ),
              AppGaps.gap16,
            ],

            // Title
            Text(
              title,
              style: AppTextStyles.title.copyWith(
                fontWeight: FontWeight.bold,
                color: isDestructive ? Colors.red : Colors.black,
              ),
              textAlign: TextAlign.center,
            ),
            AppGaps.gap12,

            // Message
            Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: ColorManager.greyText,
              ),
              textAlign: TextAlign.center,
            ),
            AppGaps.gap24,

            // Action Buttons
            Row(
              children: [
                // Cancel Button
                Expanded(
                  child: Button(
                    label: cancelText ?? context.tr.cancel,
                    onPressed: () {
                      Navigator.of(context).pop(false);
                      onCancel?.call();
                    },
                    color: cancelButtonColor ?? Colors.grey.shade300,
                    isWhiteText: false,
                    textColor: Colors.black87,
                  ),
                ),
                AppGaps.gap12,
                // Confirm Button
                Expanded(
                  flex: 2,
                  child: Button(
                    label: confirmText ?? context.tr.confirm,
                    onPressed: () {
                      Navigator.of(context).pop(true);
                      onConfirm?.call();
                    },
                    color: confirmButtonColor ??
                        (isDestructive
                            ? Colors.red
                            : ColorManager.primaryColor),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Show a basic confirm dialog
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    Color? confirmButtonColor,
    Color? cancelButtonColor,
    IconData? icon,
    Color? iconColor,
    bool isDestructive = false,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => BaseConfirmDialog(
        title: title,
        message: message,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: onConfirm,
        onCancel: onCancel,
        confirmButtonColor: confirmButtonColor,
        cancelButtonColor: cancelButtonColor,
        icon: icon,
        iconColor: iconColor,
        isDestructive: isDestructive,
      ),
    );
  }

  /// Show a destructive action confirm dialog (red theme)
  static Future<bool?> showDestructive({
    required BuildContext context,
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    IconData? icon,
  }) {
    return show(
      context: context,
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: cancelText,
      onConfirm: onConfirm,
      onCancel: onCancel,
      icon: icon ?? Icons.warning,
      isDestructive: true,
    );
  }

  /// Show a location permission confirm dialog
  static Future<bool?> showLocationConfirm({
    required BuildContext context,
    required String title,
    required String message,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return show(
      context: context,
      title: title,
      message: message,
      confirmText: confirmText,
      cancelText: cancelText,
      onConfirm: onConfirm,
      onCancel: onCancel,
      icon: Icons.location_on,
      iconColor: ColorManager.primaryColor,
    );
  }
}
