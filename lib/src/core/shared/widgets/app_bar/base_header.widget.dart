import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:luster/generated/assets.gen.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:luster/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseHeaderWidget extends StatelessWidget {
  final String path;
  final Widget child;
  final double? topPadding;

  final bool withBackButton;

  const BaseHeaderWidget({
    super.key,
    required this.path,
    required this.child,
    this.topPadding,
    this.withBackButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: context.height,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/background_images/$path.png'),
              fit: BoxFit.fill,
            ),
          ),
          child: SafeArea(
              child: Padding(
            padding:
                EdgeInsets.only(top: context.height * 0.14 + (topPadding ?? 0)),
            child: child,
          )),
        ),

        //back button
        if (withBackButton)
          Padding(
            padding: const EdgeInsets.only(top: 30, right: 24),
            child: InkWell(
              onTap: () => context.back(),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
      ],
    );
  }
}

// import 'dart:io';
//
// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:luster/generated/assets.gen.dart';
// import 'package:luster/src/core/shared/extensions/context_extensions.dart';
// import 'package:luster/src/core/theme/color_manager.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class BaseHeaderWidget extends StatelessWidget {
//   final String? title;
//   final bool withBackButton;
//
//   const BaseHeaderWidget({
//     super.key,
//     this.title,
//     this.withBackButton = false,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     // Calculate responsive dimensions
//     final double headerHeight = kIsWeb
//         ? 130.0
//         : Platform.isIOS
//             ? 120.0
//             : 130.0;
//
//     final double logoSize = kIsWeb ? 55.0 : 55.0;
//     final double horizontalPadding = 24.0;
//     final double verticalPadding = kIsWeb
//         ? 60.0
//         : Platform.isIOS
//             ? 50.0
//             : 24.0;
//
//     return AnnotatedRegion<SystemUiOverlayStyle>(
//       value: SystemUiOverlayStyle.light.copyWith(
//         statusBarColor: ColorManager.primaryColor,
//         systemNavigationBarColor: Colors.white,
//         systemNavigationBarIconBrightness: Brightness.dark,
//       ),
//       child: SizedBox(
//         height: headerHeight,
//         child: Stack(
//           children: [
//             // Background with curved design - constrained to header height
//             Positioned.fill(
//               child: ClipRect(
//                 child: Assets.images.topClipper.image(
//                   width: double.infinity,
//                   height: headerHeight,
//                   fit: BoxFit.cover,
//                 ),
//               ),
//             ),
//             // Content
//             SafeArea(
//               child: Padding(
//                 padding: EdgeInsets.only(
//                   right: horizontalPadding,
//                   left: horizontalPadding,
//                   top: verticalPadding,
//                 ),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     // Title and back button section
//                     Expanded(
//                       child: Row(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         children: [
//                           if (withBackButton)
//                             IconButton(
//                               padding: EdgeInsets.zero,
//                               constraints: const BoxConstraints(),
//                               onPressed: () => context.back(),
//                               icon: const Icon(
//                                 Icons.arrow_back_ios,
//                                 color: Colors.white,
//                                 size: 24,
//                               ),
//                             ),
//                           if (withBackButton && title != null)
//                             const SizedBox(width: 8),
//                           if (title != null)
//                             Flexible(
//                               child: Text(
//                                 title!,
//                                 style: AppTextStyles.title.copyWith(
//                                   color: ColorManager.white,
//                                   fontSize: withBackButton ? 22 : 28,
//                                   fontWeight: FontWeight.bold,
//                                 ),
//                                 maxLines: 1,
//                                 overflow: TextOverflow.ellipsis,
//                               ),
//                             ),
//                         ],
//                       ),
//                     ),
//                     // Logo section
//                     const SizedBox(width: 16),
//                     Assets.images.logoSymbol.image(
//                       width: logoSize,
//                       height: logoSize,
//                       fit: BoxFit.contain,
//                       errorBuilder: (context, error, stackTrace) {
//                         return Icon(
//                           Icons.account_balance,
//                           color: ColorManager.white,
//                           size: logoSize,
//                         );
//                       },
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
