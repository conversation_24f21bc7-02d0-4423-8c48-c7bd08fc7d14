import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:luster/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

class SearchBarWidget extends StatelessWidget {
  final String? label;

  const SearchBarWidget({
    super.key,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return SearchBar(
      hintText: label ?? context.tr.search,
      padding: const WidgetStatePropertyAll(
          EdgeInsets.symmetric(horizontal: AppSpaces.padding16)),
      leading: const Icon(CupertinoIcons.search),
    );
  }
}
