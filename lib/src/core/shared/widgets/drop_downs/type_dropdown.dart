// import 'package:flutter/material.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:luster/src/core/shared/extensions/context_extensions.dart';
// import 'package:luster/src/core/shared/extensions/riverpod_extensions.dart';
// import 'package:luster/src/screens/orders/models/type.model.dart';
// import 'package:luster/src/screens/orders/providers/orders_providers.dart';
// import 'package:xr_helper/xr_helper.dart';
// import '../../../theme/color_manager.dart';
// import '../fields/base_drop_down.dart' as local;
//
// class TypeDropdown extends ConsumerWidget {
//   final String label;
//   final ValueNotifier<OrderType?> valueNotifier;
//
//   const TypeDropdown({
//     super.key,
//     required this.label,
//     required this.valueNotifier,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final typesAsync = ref.watch(getTypesFutureProvider);
//
//     return typesAsync.get<Widget>(
//       data: (types) {
//         return Container(
//           padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
//           decoration: BoxDecoration(
//               border: Border(
//             bottom: BorderSide(
//               color: ColorManager.primaryColor,
//               width: 1,
//             ),
//           )),
//           child: local.BaseDropDown<OrderType>(
//             hint: label,
//             value: valueNotifier.value,
//             isExpanded: true,
//             data: types.map((type) {
//               return DropdownMenuItem<OrderType>(
//                 value: type,
//                 child: Text(
//                   type.name,
//                   style: AppTextStyles.labelMedium.copyWith(
//                     fontSize: 16,
//                     color: Colors.black87,
//                   ),
//                 ),
//               );
//             }).toList(),
//             onChanged: (selectedType) {
//               valueNotifier.value = selectedType as OrderType?;
//             },
//           ),
//         );
//       },
//       error: (error, stackTrace) {
//         return Container(
//           padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
//           decoration: BoxDecoration(
//             borderRadius: BorderRadius.circular(12),
//             border: Border.all(color: Colors.grey),
//           ),
//           child: Row(
//             children: [
//               Expanded(
//                 child: Text(
//                   context.tr.error,
//                   style: const TextStyle(
//                     fontSize: 16,
//                     color: Colors.red,
//                   ),
//                 ),
//               ),
//               const Icon(Icons.error, color: Colors.red),
//             ],
//           ),
//         );
//       },
//     );
//   }
// }
