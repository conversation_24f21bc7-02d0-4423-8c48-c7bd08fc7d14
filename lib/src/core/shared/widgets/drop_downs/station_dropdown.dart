// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
//
// import 'package:luster/src/core/shared/services/location_service.dart';
// import 'package:luster/src/screens/orders/models/station.model.dart';
// import 'package:luster/src/screens/orders/providers/orders_providers.dart';
// import '../fields/base_search_sheet.dart';
// import '../loading/loading_widget.dart';
//
// final stations = ValueNotifier<List<Station>>([]);
//
// class StationDropdown extends HookConsumerWidget {
//   final String label;
//   final ValueNotifier<Station?> selectedStation;
//   final Widget? icon;
//   final ValueNotifier<Station?>? excludedStation;
//
//   const StationDropdown({
//     super.key,
//     required this.label,
//     required this.selectedStation,
//     this.icon,
//     this.excludedStation,
//   });
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final ordersController = ref.watch(ordersControllerProvider);
//
//     useEffect(() {
//       // Get current location when widget mounts
//       if (stations.value.isEmpty) {
//         ordersController.getStationsWithLocation().then((value) {
//           stations.value = value;
//         });
//
//         if (LocationService.currentLocation.value == null) {
//           LocationService.getCurrentLocation().then((value) {
//             ordersController
//                 .getStationsWithLocation(
//               lat: value?.latitude,
//               lng: value?.longitude,
//             )
//                 .then((value) {
//               stations.value = value;
//             });
//           });
//         } else {
//           ordersController.getStationsWithLocation().then((value) {
//             stations.value = value;
//           });
//         }
//       }
//       return () {};
//     }, []);
//
//     return ValueListenableBuilder(
//         valueListenable: stations,
//         builder: (context, stationsList, child) {
//           if (ordersController.isLoading && stationsList.isEmpty) {
//             return const LoadingWidget();
//           }
//
//           // Filter out the excluded station if provided
//           final filteredStations = excludedStation?.value != null
//               ? stationsList
//                   .where((station) => station.id != excludedStation!.value!.id)
//                   .toList()
//               : stationsList;
//
//           return BaseSearchSheet(
//             label: label,
//             selectedValue: selectedStation.value,
//             data: filteredStations,
//             icon: icon,
//             itemModelAsName: (station) => (station as Station).name,
//             onChanged: (selectedStation) {
//               this.selectedStation.value = selectedStation as Station?;
//             },
//           );
//         });
//   }
// }
