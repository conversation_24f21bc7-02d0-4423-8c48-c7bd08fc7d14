import 'package:luster/src/core/shared/controllers/page_content_controller.dart';
import 'package:luster/src/core/shared/models/page_content_model.dart';
import 'package:luster/src/core/shared/providers/network_api_service_provider.dart';
import 'package:luster/src/core/shared/repositories/page_content_repository.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// * Repository Provider
final pageContentRepositoryProvider = Provider<PageContentRepository>((ref) {
  return PageContentRepository(
    networkApiService: ref.read(networkServiceProvider),
  );
});

// * Controller Provider
final pageContentControllerProvider =
    StateNotifierProvider<PageContentController, AsyncValue<PageContentModel?>>(
  (ref) => PageContentController(
    ref.read(pageContentRepositoryProvider),
  ),
);
