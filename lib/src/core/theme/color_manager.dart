import 'package:flutter/material.dart';

class ColorManager {
  static const backgroundColor = Colors.white;
  // Color(0xFFECF0FF);
  static const primaryColor = Color(0xFF0C428B); // Luster dark blue
  static final lightPrimaryColor = primaryColor.withOpacity(0.6);
  static const primaryOpacityContainer =
      Color(0x1A0C428B); // 10% opacity for containers
  static final orangeColor = Colors.orange;

  static const secondaryColor = Color(0xFFECF0FF);

  static const buttonColor = secondaryColor;
  static final containerColor = Colors.grey.withOpacity(0.1);
  static const selectedContainerColor = Color(0xFFD3E1E2);
  static const fieldColor = Color(0xFFCBD5E1);
  static const white = Color(0xFFFFFFFF);
  static const black = Color(0xFF000000);
  static const grey = Color(0xFFf5f5f5);
  static const greyIcon = Color(0xFF9E9E9E);
  static const greyText = Color(0xFF757575);
  static const highlightColor = Color(0xFFFFFFFF);
  static const lightGrey = Color(0xFFEEF1F6);

  static const shimmerBaseColor = Color(0xFFCECECE);
  static const cardColor = Color(0xFFEDEDED);
  static const darkGrey = Color(0xFFA4A4A4);
  static const darkBlue = Color(0xFF23292F);
  static const iconColor = Color(0xFF727272);
  static const errorColor = Color(0xFFE74C3C);
  static const successColor = Color(0xFF2ECC71);

  static const disabledButtonColor = Color(0xFFC4C4C4);
  static const termsLinkColor = Color(0xFF3366CC);
  static const greyBorder = Color(0xFFE0E0E0);
}
