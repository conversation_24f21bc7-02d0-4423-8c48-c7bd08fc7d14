// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(message) => "Error: ${message}";

  static String m1(number) => "Step ${number}";

  static String m2(count) => "${count} times";

  static String m3(name) => "Hello, ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "aboutUs": MessageLookupByLibrary.simpleMessage("About Us"),
    "action": MessageLookupByLibrary.simpleMessage("Action"),
    "actionDescription": MessageLookupByLibrary.simpleMessage(
      "Action Description",
    ),
    "addEntry": MessageLookupByLibrary.simpleMessage("Add Entry"),
    "addNewEntry": MessageLookupByLibrary.simpleMessage("Add New Entry"),
    "address": MessageLookupByLibrary.simpleMessage("Address"),
    "admin": MessageLookupByLibrary.simpleMessage("Admin"),
    "afternoon": MessageLookupByLibrary.simpleMessage("Afternoon"),
    "all": MessageLookupByLibrary.simpleMessage("All"),
    "buy": MessageLookupByLibrary.simpleMessage("Buy"),
    "buyNow": MessageLookupByLibrary.simpleMessage("Buy Now"),
    "cameraPermissionDenied": MessageLookupByLibrary.simpleMessage(
      "Camera permission denied",
    ),
    "cameraPermissionPermanentlyDenied": MessageLookupByLibrary.simpleMessage(
      "Camera permission permanently denied. Please enable it from settings",
    ),
    "cameraPermissionRequired": MessageLookupByLibrary.simpleMessage(
      "Camera permission required",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "cancelReminder": MessageLookupByLibrary.simpleMessage("Cancel Reminder"),
    "cash": MessageLookupByLibrary.simpleMessage("Cash"),
    "categories": MessageLookupByLibrary.simpleMessage("Categories"),
    "changeLocation": MessageLookupByLibrary.simpleMessage("Change Location"),
    "checkDistance": MessageLookupByLibrary.simpleMessage("Check Distance"),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "completed": MessageLookupByLibrary.simpleMessage("Completed"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmPassword": MessageLookupByLibrary.simpleMessage("Confirm Password"),
    "confirmed": MessageLookupByLibrary.simpleMessage(
      "Delivered to departure station",
    ),
    "createAccount": MessageLookupByLibrary.simpleMessage("Create Account"),
    "createPlan": MessageLookupByLibrary.simpleMessage("Create Plan"),
    "currentDay": MessageLookupByLibrary.simpleMessage("Current Day"),
    "currentPassword": MessageLookupByLibrary.simpleMessage("Current Password"),
    "dataLoadError": MessageLookupByLibrary.simpleMessage("Error loading data"),
    "day": MessageLookupByLibrary.simpleMessage("Day"),
    "days": MessageLookupByLibrary.simpleMessage("Days"),
    "deletePlan": MessageLookupByLibrary.simpleMessage("Delete Plan"),
    "deletePlanConfirmation": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this plan?",
    ),
    "deleteProduct": MessageLookupByLibrary.simpleMessage("Delete Product"),
    "deleteProductConfirmation": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to delete this product?",
    ),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage(
      "Don\'t have an account?",
    ),
    "editData": MessageLookupByLibrary.simpleMessage("Edit Data"),
    "editEntry": MessageLookupByLibrary.simpleMessage("Edit Entry"),
    "editPlan": MessageLookupByLibrary.simpleMessage("Edit Plan"),
    "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
    "email": MessageLookupByLibrary.simpleMessage("Email"),
    "enter": MessageLookupByLibrary.simpleMessage("Enter"),
    "enterAction": MessageLookupByLibrary.simpleMessage(
      "Enter action (e.g., Apply, Rinse)",
    ),
    "enterDescription": MessageLookupByLibrary.simpleMessage(
      "Enter description",
    ),
    "enterNotes": MessageLookupByLibrary.simpleMessage(
      "Enter additional notes",
    ),
    "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
      "Enter the verification code",
    ),
    "error": MessageLookupByLibrary.simpleMessage("An Error Occurred"),
    "errorMessage": m0,
    "errorOccurred": MessageLookupByLibrary.simpleMessage("An error occurred"),
    "errorUpdatingStatus": MessageLookupByLibrary.simpleMessage(
      "Error updating status",
    ),
    "evening": MessageLookupByLibrary.simpleMessage("Evening"),
    "exit": MessageLookupByLibrary.simpleMessage("Exit"),
    "exitApp": MessageLookupByLibrary.simpleMessage("Exit App"),
    "exitAppConfirmation": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to exit the app?",
    ),
    "failedToLoadPlan": MessageLookupByLibrary.simpleMessage(
      "Failed to load plan",
    ),
    "failedToLoadProduct": MessageLookupByLibrary.simpleMessage(
      "Failed to load product",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("Filter"),
    "forgotPassword": MessageLookupByLibrary.simpleMessage("Forgot Password?"),
    "forgotPasswordLink": MessageLookupByLibrary.simpleMessage(
      "Forgot Password?",
    ),
    "forgotPasswordMessage": MessageLookupByLibrary.simpleMessage(
      "Enter your phone number to send a verification code",
    ),
    "from": MessageLookupByLibrary.simpleMessage("From: "),
    "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
    "generateQR": MessageLookupByLibrary.simpleMessage("Generate QR"),
    "haveAnAccount": MessageLookupByLibrary.simpleMessage(
      "Already have an account?",
    ),
    "hideDistance": MessageLookupByLibrary.simpleMessage("Hide Distance"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "homePage": MessageLookupByLibrary.simpleMessage("Home Page"),
    "idNumber": MessageLookupByLibrary.simpleMessage("ID Number"),
    "invalidIdNumber": MessageLookupByLibrary.simpleMessage(
      "Invalid ID Number",
    ),
    "invalidPhoneFormat": MessageLookupByLibrary.simpleMessage(
      "Invalid phone format",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "Invalid Phone Number",
    ),
    "invalidQRCode": MessageLookupByLibrary.simpleMessage("Invalid QR code"),
    "loadingCategories": MessageLookupByLibrary.simpleMessage(
      "Loading categories...",
    ),
    "loadingProduct": MessageLookupByLibrary.simpleMessage(
      "Loading product...",
    ),
    "loadingProducts": MessageLookupByLibrary.simpleMessage(
      "Loading products...",
    ),
    "locationPickedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Location selected successfully",
    ),
    "login": MessageLookupByLibrary.simpleMessage("Login"),
    "loginSuccessful": MessageLookupByLibrary.simpleMessage("Login successful"),
    "loginTitle": MessageLookupByLibrary.simpleMessage("Login"),
    "logout": MessageLookupByLibrary.simpleMessage("Logout"),
    "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to log out?",
    ),
    "markAsCompleted": MessageLookupByLibrary.simpleMessage(
      "Mark as Completed",
    ),
    "mobileNumber": MessageLookupByLibrary.simpleMessage("Mobile Number"),
    "morning": MessageLookupByLibrary.simpleMessage("Morning"),
    "myProducts": MessageLookupByLibrary.simpleMessage("My Products"),
    "name": MessageLookupByLibrary.simpleMessage("Name"),
    "newPassword": MessageLookupByLibrary.simpleMessage("New Password"),
    "newPasswordMessage": MessageLookupByLibrary.simpleMessage(
      "Enter a new password",
    ),
    "newPhone": MessageLookupByLibrary.simpleMessage("New Phone Number"),
    "newest": MessageLookupByLibrary.simpleMessage("Newest"),
    "next": MessageLookupByLibrary.simpleMessage("Next"),
    "night": MessageLookupByLibrary.simpleMessage("Night"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "noDataFound": MessageLookupByLibrary.simpleMessage("No Data Found"),
    "noEntriesYet": MessageLookupByLibrary.simpleMessage("No entries yet"),
    "noNotifications": MessageLookupByLibrary.simpleMessage("No notifications"),
    "noPlanAvailable": MessageLookupByLibrary.simpleMessage(
      "No plan available",
    ),
    "noProductsFound": MessageLookupByLibrary.simpleMessage(
      "No products found",
    ),
    "noScannedProducts": MessageLookupByLibrary.simpleMessage(
      "No scanned products yet",
    ),
    "note": MessageLookupByLibrary.simpleMessage("Note"),
    "notes": MessageLookupByLibrary.simpleMessage("Notes"),
    "notificationTitle": MessageLookupByLibrary.simpleMessage(
      "Product Usage Reminder",
    ),
    "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
    "ok": MessageLookupByLibrary.simpleMessage("OK"),
    "oldest": MessageLookupByLibrary.simpleMessage("Oldest"),
    "once": MessageLookupByLibrary.simpleMessage("Once"),
    "openSettings": MessageLookupByLibrary.simpleMessage("Open Settings"),
    "password": MessageLookupByLibrary.simpleMessage("Password"),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "Passwords do not match",
    ),
    "payment": MessageLookupByLibrary.simpleMessage("Payment"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("Payment Method"),
    "pending": MessageLookupByLibrary.simpleMessage("Pending"),
    "personalInfo": MessageLookupByLibrary.simpleMessage("Personal Info"),
    "phoneHint": MessageLookupByLibrary.simpleMessage("05xxxxxxxx"),
    "phoneVerificationRequired": MessageLookupByLibrary.simpleMessage(
      "Phone verification is required for update",
    ),
    "pickFromGallery": MessageLookupByLibrary.simpleMessage(
      "Pick from Gallery",
    ),
    "pickImage": MessageLookupByLibrary.simpleMessage("Pick Image"),
    "pickLocation": MessageLookupByLibrary.simpleMessage("Pick Location"),
    "planCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Plan created successfully",
    ),
    "planDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Plan deleted successfully",
    ),
    "planEntry": MessageLookupByLibrary.simpleMessage("Plan Entry"),
    "planSaveFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to save plan",
    ),
    "planSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Plan saved successfully",
    ),
    "planUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Plan updated successfully",
    ),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "Please accept the terms and conditions",
    ),
    "pleaseAddYourLocation": MessageLookupByLibrary.simpleMessage(
      "Please add your location",
    ),
    "pleaseAddYourSocialMedia": MessageLookupByLibrary.simpleMessage(
      "Please add your social media accounts",
    ),
    "pleaseEnterAction": MessageLookupByLibrary.simpleMessage(
      "Please enter an action",
    ),
    "points": MessageLookupByLibrary.simpleMessage("Points"),
    "positionQRCode": MessageLookupByLibrary.simpleMessage(
      "Position the QR code within the frame",
    ),
    "price": MessageLookupByLibrary.simpleMessage("Price"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("Privacy Policy"),
    "productDeleted": MessageLookupByLibrary.simpleMessage(
      "Product deleted successfully",
    ),
    "productDetails": MessageLookupByLibrary.simpleMessage("Product Details"),
    "productNotFound": MessageLookupByLibrary.simpleMessage(
      "Product not found",
    ),
    "productScannedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Product scanned successfully!",
    ),
    "products": MessageLookupByLibrary.simpleMessage("Products"),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "profileUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Profile updated successfully",
    ),
    "progress": MessageLookupByLibrary.simpleMessage("Progress"),
    "qrCodeScanned": MessageLookupByLibrary.simpleMessage("QR code scanned"),
    "qrSaveFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to save QR code",
    ),
    "qrSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "QR code saved successfully",
    ),
    "register": MessageLookupByLibrary.simpleMessage("Register"),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "Registration successful",
    ),
    "rememberMe": MessageLookupByLibrary.simpleMessage("Remember Me"),
    "reminderScheduled": MessageLookupByLibrary.simpleMessage(
      "Reminder scheduled",
    ),
    "reminders": MessageLookupByLibrary.simpleMessage("Reminders"),
    "removeEntry": MessageLookupByLibrary.simpleMessage("Remove Entry"),
    "repeatCount": MessageLookupByLibrary.simpleMessage("Repeat Count"),
    "rescheduleReminder": MessageLookupByLibrary.simpleMessage(
      "Reschedule Reminder",
    ),
    "resendCode": MessageLookupByLibrary.simpleMessage("Resend Code"),
    "resetPassword": MessageLookupByLibrary.simpleMessage("Reset Password"),
    "resetPasswordButton": MessageLookupByLibrary.simpleMessage(
      "Reset Password",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
    "savePlan": MessageLookupByLibrary.simpleMessage("Save Plan"),
    "saveQR": MessageLookupByLibrary.simpleMessage("Save QR"),
    "scanProduct": MessageLookupByLibrary.simpleMessage("Scan Product"),
    "scanProductToStart": MessageLookupByLibrary.simpleMessage(
      "Scan a product QR code to start tracking your usage plan",
    ),
    "scanQRCode": MessageLookupByLibrary.simpleMessage("Scan QR Code"),
    "scanToStart": MessageLookupByLibrary.simpleMessage(
      "Scan QR code to start",
    ),
    "scannedProducts": MessageLookupByLibrary.simpleMessage("Scanned Products"),
    "scanningQRCode": MessageLookupByLibrary.simpleMessage(
      "Scanning QR code...",
    ),
    "scheduleReminder": MessageLookupByLibrary.simpleMessage(
      "Schedule Reminder",
    ),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "searchForProducts": MessageLookupByLibrary.simpleMessage(
      "Search for Products",
    ),
    "searchShipment": MessageLookupByLibrary.simpleMessage("Search Shipment"),
    "searchShipmentButton": MessageLookupByLibrary.simpleMessage(
      "Search Shipment",
    ),
    "searchShipmentDetails": MessageLookupByLibrary.simpleMessage(
      "Shipment Search Details",
    ),
    "searchShipmentTitle": MessageLookupByLibrary.simpleMessage(
      "Search Shipment",
    ),
    "seeAll": MessageLookupByLibrary.simpleMessage("See All"),
    "selectTime": MessageLookupByLibrary.simpleMessage("Select Time"),
    "shareQR": MessageLookupByLibrary.simpleMessage("Share QR"),
    "shipmentsHistory": MessageLookupByLibrary.simpleMessage(
      "Shipments History",
    ),
    "skip": MessageLookupByLibrary.simpleMessage("Skip"),
    "sortBy": MessageLookupByLibrary.simpleMessage("Sort By"),
    "startNow": MessageLookupByLibrary.simpleMessage("Start Now"),
    "statusUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Status updated successfully",
    ),
    "step": MessageLookupByLibrary.simpleMessage("Step"),
    "stepNumber": m1,
    "storagePermissionDenied": MessageLookupByLibrary.simpleMessage(
      "Storage permission denied",
    ),
    "storagePermissionRequired": MessageLookupByLibrary.simpleMessage(
      "Storage permission required",
    ),
    "store": MessageLookupByLibrary.simpleMessage("Store"),
    "storeBackground": MessageLookupByLibrary.simpleMessage("Store Background"),
    "storeLogo": MessageLookupByLibrary.simpleMessage("Store Logo"),
    "storeName": MessageLookupByLibrary.simpleMessage("Store Name"),
    "submit": MessageLookupByLibrary.simpleMessage("Submit"),
    "takePhoto": MessageLookupByLibrary.simpleMessage("Take Photo"),
    "tapToAddEntries": MessageLookupByLibrary.simpleMessage(
      "Tap + to add entries",
    ),
    "tapToSelectLocation": MessageLookupByLibrary.simpleMessage(
      "Tap to select location",
    ),
    "tasks": MessageLookupByLibrary.simpleMessage("Tasks"),
    "termsAndConditions": MessageLookupByLibrary.simpleMessage(
      "I confirm that I have read and agree to the terms and conditions of this app",
    ),
    "termsAndConditionsEnd": MessageLookupByLibrary.simpleMessage(
      "of using this app",
    ),
    "termsAndConditionsLink": MessageLookupByLibrary.simpleMessage(
      "Terms and Conditions",
    ),
    "termsAndConditionsText": MessageLookupByLibrary.simpleMessage(
      "I confirm that I have read, understood, and agree to the",
    ),
    "termsDialogContent": MessageLookupByLibrary.simpleMessage(
      "These are the terms and conditions of the app. Please read them carefully before accepting. The full content will be added later.",
    ),
    "termsDialogTitle": MessageLookupByLibrary.simpleMessage(
      "Terms and Conditions",
    ),
    "time": MessageLookupByLibrary.simpleMessage("Time"),
    "times": MessageLookupByLibrary.simpleMessage("times"),
    "timesCount": m2,
    "to": MessageLookupByLibrary.simpleMessage("To: "),
    "today": MessageLookupByLibrary.simpleMessage("Today"),
    "twice": MessageLookupByLibrary.simpleMessage("Twice"),
    "unableToGetCurrentLocation": MessageLookupByLibrary.simpleMessage(
      "Unable to get current location",
    ),
    "unknownProduct": MessageLookupByLibrary.simpleMessage("Unknown Product"),
    "unreadNotifications": MessageLookupByLibrary.simpleMessage(
      "Unread Notifications",
    ),
    "usagePlan": MessageLookupByLibrary.simpleMessage("Usage Plan"),
    "user": MessageLookupByLibrary.simpleMessage("User"),
    "verificationMessage": MessageLookupByLibrary.simpleMessage(
      "A verification code was sent to your phone number",
    ),
    "verificationTitle": MessageLookupByLibrary.simpleMessage(
      "Phone Verification",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("Verify"),
    "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
    "welcomeWithName": m3,
    "yes": MessageLookupByLibrary.simpleMessage("Yes"),
  };
}
