// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  static String m0(message) => "خطأ: ${message}";

  static String m1(number) => "خطوة ${number}";

  static String m2(count) => "${count} مرات";

  static String m3(name) => "مرحبًا، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("حول"),
    "aboutUs": MessageLookupByLibrary.simpleMessage("من نحن"),
    "action": MessageLookupByLibrary.simpleMessage("الإجراء"),
    "actionDescription": MessageLookupByLibrary.simpleMessage("وصف الإجراء"),
    "addEntry": MessageLookupByLibrary.simpleMessage("إضافة إدخال"),
    "addNewEntry": MessageLookupByLibrary.simpleMessage("إضافة إدخال جديد"),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "admin": MessageLookupByLibrary.simpleMessage("المسؤول"),
    "afternoon": MessageLookupByLibrary.simpleMessage("ظهراً"),
    "all": MessageLookupByLibrary.simpleMessage("الكل"),
    "buy": MessageLookupByLibrary.simpleMessage("شراء"),
    "buyNow": MessageLookupByLibrary.simpleMessage("اشتر الآن"),
    "cameraPermissionDenied": MessageLookupByLibrary.simpleMessage(
      "تم رفض إذن الكاميرا",
    ),
    "cameraPermissionPermanentlyDenied": MessageLookupByLibrary.simpleMessage(
      "تم رفض إذن الكاميرا بشكل دائم. يرجى تمكينه من الإعدادات",
    ),
    "cameraPermissionRequired": MessageLookupByLibrary.simpleMessage(
      "مطلوب إذن الكاميرا",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "cancelReminder": MessageLookupByLibrary.simpleMessage("إلغاء التذكير"),
    "cash": MessageLookupByLibrary.simpleMessage("نقدًا"),
    "categories": MessageLookupByLibrary.simpleMessage("الفئات"),
    "changeLocation": MessageLookupByLibrary.simpleMessage("تغيير الموقع"),
    "checkDistance": MessageLookupByLibrary.simpleMessage("تحقق من المسافة"),
    "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
    "completed": MessageLookupByLibrary.simpleMessage("مكتمل"),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "confirmed": MessageLookupByLibrary.simpleMessage(
      "تم التسليم إلى محطة الانطلاق",
    ),
    "createAccount": MessageLookupByLibrary.simpleMessage("إنشاء حساب"),
    "createPlan": MessageLookupByLibrary.simpleMessage("إنشاء خطة"),
    "currentDay": MessageLookupByLibrary.simpleMessage("اليوم الحالي"),
    "currentPassword": MessageLookupByLibrary.simpleMessage(
      "كلمة المرور الحالية",
    ),
    "dataLoadError": MessageLookupByLibrary.simpleMessage(
      "خطأ في تحميل البيانات",
    ),
    "day": MessageLookupByLibrary.simpleMessage("اليوم"),
    "days": MessageLookupByLibrary.simpleMessage("أيام"),
    "deletePlan": MessageLookupByLibrary.simpleMessage("حذف الخطة"),
    "deletePlanConfirmation": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذه الخطة؟",
    ),
    "deleteProduct": MessageLookupByLibrary.simpleMessage("حذف المنتج"),
    "deleteProductConfirmation": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد حذف هذا المنتج؟",
    ),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "editData": MessageLookupByLibrary.simpleMessage("تعديل البيانات"),
    "editEntry": MessageLookupByLibrary.simpleMessage("تعديل الإدخال"),
    "editPlan": MessageLookupByLibrary.simpleMessage("تعديل الخطة"),
    "editProfile": MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterAction": MessageLookupByLibrary.simpleMessage(
      "أدخل الإجراء (مثل: تطبيق، شطف)",
    ),
    "enterDescription": MessageLookupByLibrary.simpleMessage("أدخل الوصف"),
    "enterNotes": MessageLookupByLibrary.simpleMessage("أدخل ملاحظات إضافية"),
    "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
      "أدخل رمز التحقق",
    ),
    "error": MessageLookupByLibrary.simpleMessage("حدث خطأ"),
    "errorMessage": m0,
    "errorOccurred": MessageLookupByLibrary.simpleMessage("حدث خطأ"),
    "errorUpdatingStatus": MessageLookupByLibrary.simpleMessage(
      "حدث خطأ أثناء تحديث الحالة",
    ),
    "evening": MessageLookupByLibrary.simpleMessage("مساءً"),
    "exit": MessageLookupByLibrary.simpleMessage("خروج"),
    "exitApp": MessageLookupByLibrary.simpleMessage("الخروج من التطبيق"),
    "exitAppConfirmation": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد الخروج من التطبيق؟",
    ),
    "failedToLoadPlan": MessageLookupByLibrary.simpleMessage("فشل تحميل الخطة"),
    "failedToLoadProduct": MessageLookupByLibrary.simpleMessage(
      "فشل تحميل المنتج",
    ),
    "filter": MessageLookupByLibrary.simpleMessage("تصفية"),
    "forgotPassword": MessageLookupByLibrary.simpleMessage(
      "هل نسيت كلمة المرور؟",
    ),
    "forgotPasswordLink": MessageLookupByLibrary.simpleMessage(
      "هل نسيت كلمة المرور؟",
    ),
    "forgotPasswordMessage": MessageLookupByLibrary.simpleMessage(
      "أدخل رقم جوالك لإرسال رمز التحقق",
    ),
    "from": MessageLookupByLibrary.simpleMessage("من: "),
    "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
    "generateQR": MessageLookupByLibrary.simpleMessage("إنشاء رمز QR"),
    "haveAnAccount": MessageLookupByLibrary.simpleMessage(
      "هل لديك حساب بالفعل؟",
    ),
    "hideDistance": MessageLookupByLibrary.simpleMessage("إخفاء المسافة"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "homePage": MessageLookupByLibrary.simpleMessage("الصفحة الرئيسية"),
    "idNumber": MessageLookupByLibrary.simpleMessage("رقم الهوية"),
    "invalidIdNumber": MessageLookupByLibrary.simpleMessage(
      "رقم الهوية غير صالح",
    ),
    "invalidPhoneFormat": MessageLookupByLibrary.simpleMessage(
      "تنسيق رقم الجوال غير صحيح",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "رقم الجوال غير صالح",
    ),
    "invalidQRCode": MessageLookupByLibrary.simpleMessage("رمز QR غير صالح"),
    "loadingCategories": MessageLookupByLibrary.simpleMessage(
      "جاري تحميل الفئات...",
    ),
    "loadingProduct": MessageLookupByLibrary.simpleMessage(
      "جاري تحميل المنتج...",
    ),
    "loadingProducts": MessageLookupByLibrary.simpleMessage(
      "جاري تحميل المنتجات...",
    ),
    "locationPickedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم اختيار الموقع بنجاح",
    ),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم تسجيل الدخول بنجاح",
    ),
    "loginTitle": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد أنك تريد تسجيل الخروج؟",
    ),
    "markAsCompleted": MessageLookupByLibrary.simpleMessage("وضع علامة كمكتمل"),
    "mobileNumber": MessageLookupByLibrary.simpleMessage("رقم الجوال"),
    "morning": MessageLookupByLibrary.simpleMessage("صباحاً"),
    "myProducts": MessageLookupByLibrary.simpleMessage("منتجاتي"),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "newPassword": MessageLookupByLibrary.simpleMessage("كلمة المرور الجديدة"),
    "newPasswordMessage": MessageLookupByLibrary.simpleMessage(
      "أدخل كلمة مرور جديدة",
    ),
    "newPhone": MessageLookupByLibrary.simpleMessage("رقم الجوال الجديد"),
    "newest": MessageLookupByLibrary.simpleMessage("الأحدث"),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "night": MessageLookupByLibrary.simpleMessage("ليلاً"),
    "no": MessageLookupByLibrary.simpleMessage("لا"),
    "noDataFound": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
    "noEntriesYet": MessageLookupByLibrary.simpleMessage("لا توجد إدخالات بعد"),
    "noNotifications": MessageLookupByLibrary.simpleMessage("لا توجد إشعارات"),
    "noPlanAvailable": MessageLookupByLibrary.simpleMessage(
      "لا توجد خطة متاحة",
    ),
    "noProductsFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على منتجات",
    ),
    "noScannedProducts": MessageLookupByLibrary.simpleMessage(
      "لا توجد منتجات ممسوحة بعد",
    ),
    "note": MessageLookupByLibrary.simpleMessage("ملاحظة"),
    "notes": MessageLookupByLibrary.simpleMessage("ملاحظات"),
    "notificationTitle": MessageLookupByLibrary.simpleMessage(
      "تذكير استخدام المنتج",
    ),
    "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
    "ok": MessageLookupByLibrary.simpleMessage("موافق"),
    "oldest": MessageLookupByLibrary.simpleMessage("الأقدم"),
    "once": MessageLookupByLibrary.simpleMessage("مرة واحدة"),
    "openSettings": MessageLookupByLibrary.simpleMessage("فتح الإعدادات"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "كلمتا المرور غير متطابقتان",
    ),
    "payment": MessageLookupByLibrary.simpleMessage("الدفع"),
    "paymentMethod": MessageLookupByLibrary.simpleMessage("طريقة الدفع"),
    "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
    "personalInfo": MessageLookupByLibrary.simpleMessage("المعلومات الشخصية"),
    "phoneHint": MessageLookupByLibrary.simpleMessage("05xxxxxxxx"),
    "phoneVerificationRequired": MessageLookupByLibrary.simpleMessage(
      "يتطلب التحديث التحقق من رقم الجوال",
    ),
    "pickFromGallery": MessageLookupByLibrary.simpleMessage("اختيار من المعرض"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختيار صورة"),
    "pickLocation": MessageLookupByLibrary.simpleMessage("اختيار الموقع"),
    "planCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إنشاء الخطة بنجاح",
    ),
    "planDeletedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حذف الخطة بنجاح",
    ),
    "planEntry": MessageLookupByLibrary.simpleMessage("إدخال الخطة"),
    "planSaveFailed": MessageLookupByLibrary.simpleMessage("فشل حفظ الخطة"),
    "planSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حفظ الخطة بنجاح",
    ),
    "planUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الخطة بنجاح",
    ),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "يرجى قبول الشروط والأحكام",
    ),
    "pleaseAddYourLocation": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة موقعك",
    ),
    "pleaseAddYourSocialMedia": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة حساباتك على مواقع التواصل الاجتماعي",
    ),
    "pleaseEnterAction": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال إجراء",
    ),
    "points": MessageLookupByLibrary.simpleMessage("نقاط"),
    "positionQRCode": MessageLookupByLibrary.simpleMessage(
      "ضع رمز QR داخل الإطار",
    ),
    "price": MessageLookupByLibrary.simpleMessage("السعر"),
    "privacyPolicy": MessageLookupByLibrary.simpleMessage("سياسة الخصوصية"),
    "productDeleted": MessageLookupByLibrary.simpleMessage(
      "تم حذف المنتج بنجاح",
    ),
    "productDetails": MessageLookupByLibrary.simpleMessage("تفاصيل المنتج"),
    "productNotFound": MessageLookupByLibrary.simpleMessage("المنتج غير موجود"),
    "productScannedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم مسح المنتج بنجاح!",
    ),
    "products": MessageLookupByLibrary.simpleMessage("المنتجات"),
    "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
    "profileUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الملف الشخصي بنجاح",
    ),
    "progress": MessageLookupByLibrary.simpleMessage("التقدم"),
    "qrCodeScanned": MessageLookupByLibrary.simpleMessage("تم مسح رمز QR"),
    "qrSaveFailed": MessageLookupByLibrary.simpleMessage("فشل حفظ رمز QR"),
    "qrSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حفظ رمز QR بنجاح",
    ),
    "register": MessageLookupByLibrary.simpleMessage("إنشاء حساب"),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم إنشاء الحساب بنجاح",
    ),
    "rememberMe": MessageLookupByLibrary.simpleMessage("تذكرني"),
    "reminderScheduled": MessageLookupByLibrary.simpleMessage(
      "تم جدولة التذكير",
    ),
    "reminders": MessageLookupByLibrary.simpleMessage("التذكيرات"),
    "removeEntry": MessageLookupByLibrary.simpleMessage("إزالة إدخال"),
    "repeatCount": MessageLookupByLibrary.simpleMessage("عدد التكرار"),
    "rescheduleReminder": MessageLookupByLibrary.simpleMessage(
      "إعادة جدولة التذكير",
    ),
    "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الرمز"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة المرور",
    ),
    "resetPasswordButton": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة المرور",
    ),
    "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
    "savePlan": MessageLookupByLibrary.simpleMessage("حفظ الخطة"),
    "saveQR": MessageLookupByLibrary.simpleMessage("حفظ رمز QR"),
    "scanProduct": MessageLookupByLibrary.simpleMessage("مسح المنتج"),
    "scanProductToStart": MessageLookupByLibrary.simpleMessage(
      "امسح رمز QR للمنتج لبدء تتبع خطة الاستخدام",
    ),
    "scanQRCode": MessageLookupByLibrary.simpleMessage("مسح رمز QR"),
    "scanToStart": MessageLookupByLibrary.simpleMessage("امسح رمز QR للبدء"),
    "scannedProducts": MessageLookupByLibrary.simpleMessage(
      "المنتجات الممسوحة",
    ),
    "scanningQRCode": MessageLookupByLibrary.simpleMessage(
      "جاري مسح رمز QR...",
    ),
    "scheduleReminder": MessageLookupByLibrary.simpleMessage("جدولة تذكير"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "searchForProducts": MessageLookupByLibrary.simpleMessage(
      "ابحث عن المنتجات",
    ),
    "searchShipment": MessageLookupByLibrary.simpleMessage("بحث عن شحنة"),
    "searchShipmentButton": MessageLookupByLibrary.simpleMessage(
      "ابحث عن الشحنة",
    ),
    "searchShipmentDetails": MessageLookupByLibrary.simpleMessage(
      "تفاصيل البحث عن الشحنة",
    ),
    "searchShipmentTitle": MessageLookupByLibrary.simpleMessage(
      "البحث عن شحنة",
    ),
    "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
    "selectTime": MessageLookupByLibrary.simpleMessage("اختر الوقت"),
    "shareQR": MessageLookupByLibrary.simpleMessage("مشاركة رمز QR"),
    "shipmentsHistory": MessageLookupByLibrary.simpleMessage("سجل الشحنات"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "sortBy": MessageLookupByLibrary.simpleMessage("ترتيب حسب"),
    "startNow": MessageLookupByLibrary.simpleMessage("ابدأ الآن"),
    "statusUpdatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم تحديث الحالة بنجاح",
    ),
    "step": MessageLookupByLibrary.simpleMessage("خطوة"),
    "stepNumber": m1,
    "storagePermissionDenied": MessageLookupByLibrary.simpleMessage(
      "تم رفض إذن التخزين",
    ),
    "storagePermissionRequired": MessageLookupByLibrary.simpleMessage(
      "مطلوب إذن التخزين",
    ),
    "store": MessageLookupByLibrary.simpleMessage("المتجر"),
    "storeBackground": MessageLookupByLibrary.simpleMessage("خلفية المتجر"),
    "storeLogo": MessageLookupByLibrary.simpleMessage("شعار المتجر"),
    "storeName": MessageLookupByLibrary.simpleMessage("اسم المتجر"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "takePhoto": MessageLookupByLibrary.simpleMessage("التقاط صورة"),
    "tapToAddEntries": MessageLookupByLibrary.simpleMessage(
      "اضغط + لإضافة إدخالات",
    ),
    "tapToSelectLocation": MessageLookupByLibrary.simpleMessage(
      "اضغط لتحديد الموقع",
    ),
    "tasks": MessageLookupByLibrary.simpleMessage("المهام"),
    "termsAndConditions": MessageLookupByLibrary.simpleMessage(
      "أؤكد أنني قرأت وأوافق على الشروط والأحكام الخاصة بهذا التطبيق",
    ),
    "termsAndConditionsEnd": MessageLookupByLibrary.simpleMessage(
      "الخاصة باستخدام هذا التطبيق",
    ),
    "termsAndConditionsLink": MessageLookupByLibrary.simpleMessage(
      "الشروط والأحكام",
    ),
    "termsAndConditionsText": MessageLookupByLibrary.simpleMessage(
      "أؤكد أنني قرأت وفهمت وأوافق على",
    ),
    "termsDialogContent": MessageLookupByLibrary.simpleMessage(
      "هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة. سيتم إضافة المحتوى الكامل لاحقًا.",
    ),
    "termsDialogTitle": MessageLookupByLibrary.simpleMessage("الشروط والأحكام"),
    "time": MessageLookupByLibrary.simpleMessage("الوقت"),
    "times": MessageLookupByLibrary.simpleMessage("مرات"),
    "timesCount": m2,
    "to": MessageLookupByLibrary.simpleMessage("إلى: "),
    "today": MessageLookupByLibrary.simpleMessage("اليوم"),
    "twice": MessageLookupByLibrary.simpleMessage("مرتين"),
    "unableToGetCurrentLocation": MessageLookupByLibrary.simpleMessage(
      "تعذر الحصول على الموقع الحالي",
    ),
    "unknownProduct": MessageLookupByLibrary.simpleMessage("منتج غير معروف"),
    "unreadNotifications": MessageLookupByLibrary.simpleMessage(
      "إشعارات غير مقروءة",
    ),
    "usagePlan": MessageLookupByLibrary.simpleMessage("خطة الاستخدام"),
    "user": MessageLookupByLibrary.simpleMessage("المستخدم"),
    "verificationMessage": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رمز التحقق إلى رقم جوالك",
    ),
    "verificationTitle": MessageLookupByLibrary.simpleMessage(
      "تأكيد رقم الجوال",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("تحقق"),
    "viewAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
    "welcomeWithName": m3,
    "yes": MessageLookupByLibrary.simpleMessage("نعم"),
  };
}
