// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Login`
  String get login {
    return Intl.message('Login', name: 'login', desc: '', args: []);
  }

  /// `Register`
  String get register {
    return Intl.message('Register', name: 'register', desc: '', args: []);
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `Don't have an account?`
  String get dontHaveAnAccount {
    return Intl.message(
      'Don\'t have an account?',
      name: 'dontHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `Already have an account?`
  String get haveAnAccount {
    return Intl.message(
      'Already have an account?',
      name: 'haveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message('Password', name: 'password', desc: '', args: []);
  }

  /// `Confirm Password`
  String get confirmPassword {
    return Intl.message(
      'Confirm Password',
      name: 'confirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `Forgot Password?`
  String get forgotPassword {
    return Intl.message(
      'Forgot Password?',
      name: 'forgotPassword',
      desc: '',
      args: [],
    );
  }

  /// `Reset Password`
  String get resetPassword {
    return Intl.message(
      'Reset Password',
      name: 'resetPassword',
      desc: '',
      args: [],
    );
  }

  /// `Mobile Number`
  String get mobileNumber {
    return Intl.message(
      'Mobile Number',
      name: 'mobileNumber',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get skip {
    return Intl.message('Skip', name: 'skip', desc: '', args: []);
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Full Name`
  String get fullName {
    return Intl.message('Full Name', name: 'fullName', desc: '', args: []);
  }

  /// `See All`
  String get seeAll {
    return Intl.message('See All', name: 'seeAll', desc: '', args: []);
  }

  /// `Hello, {name}`
  String welcomeWithName(Object name) {
    return Intl.message(
      'Hello, $name',
      name: 'welcomeWithName',
      desc: '',
      args: [name],
    );
  }

  /// `About`
  String get about {
    return Intl.message('About', name: 'about', desc: '', args: []);
  }

  /// `Next`
  String get next {
    return Intl.message('Next', name: 'next', desc: '', args: []);
  }

  /// `Start Now`
  String get startNow {
    return Intl.message('Start Now', name: 'startNow', desc: '', args: []);
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `Address`
  String get address {
    return Intl.message('Address', name: 'address', desc: '', args: []);
  }

  /// `Enter`
  String get enter {
    return Intl.message('Enter', name: 'enter', desc: '', args: []);
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `Pick Image`
  String get pickImage {
    return Intl.message('Pick Image', name: 'pickImage', desc: '', args: []);
  }

  /// `Location selected successfully`
  String get locationPickedSuccessfully {
    return Intl.message(
      'Location selected successfully',
      name: 'locationPickedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Pick Location`
  String get pickLocation {
    return Intl.message(
      'Pick Location',
      name: 'pickLocation',
      desc: '',
      args: [],
    );
  }

  /// `Tap to select location`
  String get tapToSelectLocation {
    return Intl.message(
      'Tap to select location',
      name: 'tapToSelectLocation',
      desc: '',
      args: [],
    );
  }

  /// `Change Location`
  String get changeLocation {
    return Intl.message(
      'Change Location',
      name: 'changeLocation',
      desc: '',
      args: [],
    );
  }

  /// `No Data Found`
  String get noDataFound {
    return Intl.message(
      'No Data Found',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `Please add your social media accounts`
  String get pleaseAddYourSocialMedia {
    return Intl.message(
      'Please add your social media accounts',
      name: 'pleaseAddYourSocialMedia',
      desc: '',
      args: [],
    );
  }

  /// `Please add your location`
  String get pleaseAddYourLocation {
    return Intl.message(
      'Please add your location',
      name: 'pleaseAddYourLocation',
      desc: '',
      args: [],
    );
  }

  /// `Store Logo`
  String get storeLogo {
    return Intl.message('Store Logo', name: 'storeLogo', desc: '', args: []);
  }

  /// `Store Background`
  String get storeBackground {
    return Intl.message(
      'Store Background',
      name: 'storeBackground',
      desc: '',
      args: [],
    );
  }

  /// `Store Name`
  String get storeName {
    return Intl.message('Store Name', name: 'storeName', desc: '', args: []);
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Search for Products`
  String get searchForProducts {
    return Intl.message(
      'Search for Products',
      name: 'searchForProducts',
      desc: '',
      args: [],
    );
  }

  /// `Create Account`
  String get createAccount {
    return Intl.message(
      'Create Account',
      name: 'createAccount',
      desc: '',
      args: [],
    );
  }

  /// `ID Number`
  String get idNumber {
    return Intl.message('ID Number', name: 'idNumber', desc: '', args: []);
  }

  /// `I confirm that I have read and agree to the terms and conditions of this app`
  String get termsAndConditions {
    return Intl.message(
      'I confirm that I have read and agree to the terms and conditions of this app',
      name: 'termsAndConditions',
      desc: '',
      args: [],
    );
  }

  /// `Invalid ID Number`
  String get invalidIdNumber {
    return Intl.message(
      'Invalid ID Number',
      name: 'invalidIdNumber',
      desc: '',
      args: [],
    );
  }

  /// `Invalid Phone Number`
  String get invalidPhoneNumber {
    return Intl.message(
      'Invalid Phone Number',
      name: 'invalidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `Passwords do not match`
  String get passwordsDoNotMatch {
    return Intl.message(
      'Passwords do not match',
      name: 'passwordsDoNotMatch',
      desc: '',
      args: [],
    );
  }

  /// `Please accept the terms and conditions`
  String get pleaseAcceptTerms {
    return Intl.message(
      'Please accept the terms and conditions',
      name: 'pleaseAcceptTerms',
      desc: '',
      args: [],
    );
  }

  /// `An Error Occurred`
  String get error {
    return Intl.message('An Error Occurred', name: 'error', desc: '', args: []);
  }

  /// `I confirm that I have read, understood, and agree to the`
  String get termsAndConditionsText {
    return Intl.message(
      'I confirm that I have read, understood, and agree to the',
      name: 'termsAndConditionsText',
      desc: '',
      args: [],
    );
  }

  /// `Terms and Conditions`
  String get termsAndConditionsLink {
    return Intl.message(
      'Terms and Conditions',
      name: 'termsAndConditionsLink',
      desc: '',
      args: [],
    );
  }

  /// `of using this app`
  String get termsAndConditionsEnd {
    return Intl.message(
      'of using this app',
      name: 'termsAndConditionsEnd',
      desc: '',
      args: [],
    );
  }

  /// `Terms and Conditions`
  String get termsDialogTitle {
    return Intl.message(
      'Terms and Conditions',
      name: 'termsDialogTitle',
      desc: '',
      args: [],
    );
  }

  /// `These are the terms and conditions of the app. Please read them carefully before accepting. The full content will be added later.`
  String get termsDialogContent {
    return Intl.message(
      'These are the terms and conditions of the app. Please read them carefully before accepting. The full content will be added later.',
      name: 'termsDialogContent',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get close {
    return Intl.message('Close', name: 'close', desc: '', args: []);
  }

  /// `05xxxxxxxx`
  String get phoneHint {
    return Intl.message('05xxxxxxxx', name: 'phoneHint', desc: '', args: []);
  }

  /// `Login`
  String get loginTitle {
    return Intl.message('Login', name: 'loginTitle', desc: '', args: []);
  }

  /// `Remember Me`
  String get rememberMe {
    return Intl.message('Remember Me', name: 'rememberMe', desc: '', args: []);
  }

  /// `Forgot Password?`
  String get forgotPasswordLink {
    return Intl.message(
      'Forgot Password?',
      name: 'forgotPasswordLink',
      desc: '',
      args: [],
    );
  }

  /// `Don't have an account?`
  String get dontHaveAccount {
    return Intl.message(
      'Don\'t have an account?',
      name: 'dontHaveAccount',
      desc: '',
      args: [],
    );
  }

  /// `Phone Verification`
  String get verificationTitle {
    return Intl.message(
      'Phone Verification',
      name: 'verificationTitle',
      desc: '',
      args: [],
    );
  }

  /// `A verification code was sent to your phone number`
  String get verificationMessage {
    return Intl.message(
      'A verification code was sent to your phone number',
      name: 'verificationMessage',
      desc: '',
      args: [],
    );
  }

  /// `Enter the verification code`
  String get enterVerificationCode {
    return Intl.message(
      'Enter the verification code',
      name: 'enterVerificationCode',
      desc: '',
      args: [],
    );
  }

  /// `Verify`
  String get verify {
    return Intl.message('Verify', name: 'verify', desc: '', args: []);
  }

  /// `Resend Code`
  String get resendCode {
    return Intl.message('Resend Code', name: 'resendCode', desc: '', args: []);
  }

  /// `Login successful`
  String get loginSuccessful {
    return Intl.message(
      'Login successful',
      name: 'loginSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Registration successful`
  String get registrationSuccessful {
    return Intl.message(
      'Registration successful',
      name: 'registrationSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Error loading data`
  String get dataLoadError {
    return Intl.message(
      'Error loading data',
      name: 'dataLoadError',
      desc: '',
      args: [],
    );
  }

  /// `From: `
  String get from {
    return Intl.message('From: ', name: 'from', desc: '', args: []);
  }

  /// `To: `
  String get to {
    return Intl.message('To: ', name: 'to', desc: '', args: []);
  }

  /// `Home Page`
  String get homePage {
    return Intl.message('Home Page', name: 'homePage', desc: '', args: []);
  }

  /// `Shipments History`
  String get shipmentsHistory {
    return Intl.message(
      'Shipments History',
      name: 'shipmentsHistory',
      desc: '',
      args: [],
    );
  }

  /// `Payment`
  String get payment {
    return Intl.message('Payment', name: 'payment', desc: '', args: []);
  }

  /// `Notes`
  String get notes {
    return Intl.message('Notes', name: 'notes', desc: '', args: []);
  }

  /// `Note`
  String get note {
    return Intl.message('Note', name: 'note', desc: '', args: []);
  }

  /// `Edit Data`
  String get editData {
    return Intl.message('Edit Data', name: 'editData', desc: '', args: []);
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Invalid phone format`
  String get invalidPhoneFormat {
    return Intl.message(
      'Invalid phone format',
      name: 'invalidPhoneFormat',
      desc: '',
      args: [],
    );
  }

  /// `Tasks`
  String get tasks {
    return Intl.message('Tasks', name: 'tasks', desc: '', args: []);
  }

  /// `Search Shipment`
  String get searchShipment {
    return Intl.message(
      'Search Shipment',
      name: 'searchShipment',
      desc: '',
      args: [],
    );
  }

  /// `Search Shipment`
  String get searchShipmentTitle {
    return Intl.message(
      'Search Shipment',
      name: 'searchShipmentTitle',
      desc: '',
      args: [],
    );
  }

  /// `Search Shipment`
  String get searchShipmentButton {
    return Intl.message(
      'Search Shipment',
      name: 'searchShipmentButton',
      desc: '',
      args: [],
    );
  }

  /// `Shipment Search Details`
  String get searchShipmentDetails {
    return Intl.message(
      'Shipment Search Details',
      name: 'searchShipmentDetails',
      desc: '',
      args: [],
    );
  }

  /// `Unable to get current location`
  String get unableToGetCurrentLocation {
    return Intl.message(
      'Unable to get current location',
      name: 'unableToGetCurrentLocation',
      desc: '',
      args: [],
    );
  }

  /// `Status updated successfully`
  String get statusUpdatedSuccessfully {
    return Intl.message(
      'Status updated successfully',
      name: 'statusUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Error updating status`
  String get errorUpdatingStatus {
    return Intl.message(
      'Error updating status',
      name: 'errorUpdatingStatus',
      desc: '',
      args: [],
    );
  }

  /// `Hide Distance`
  String get hideDistance {
    return Intl.message(
      'Hide Distance',
      name: 'hideDistance',
      desc: '',
      args: [],
    );
  }

  /// `Check Distance`
  String get checkDistance {
    return Intl.message(
      'Check Distance',
      name: 'checkDistance',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get ok {
    return Intl.message('OK', name: 'ok', desc: '', args: []);
  }

  /// `Take Photo`
  String get takePhoto {
    return Intl.message('Take Photo', name: 'takePhoto', desc: '', args: []);
  }

  /// `Pick from Gallery`
  String get pickFromGallery {
    return Intl.message(
      'Pick from Gallery',
      name: 'pickFromGallery',
      desc: '',
      args: [],
    );
  }

  /// `Delivered to departure station`
  String get confirmed {
    return Intl.message(
      'Delivered to departure station',
      name: 'confirmed',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get yes {
    return Intl.message('Yes', name: 'yes', desc: '', args: []);
  }

  /// `No`
  String get no {
    return Intl.message('No', name: 'no', desc: '', args: []);
  }

  /// `Today`
  String get today {
    return Intl.message('Today', name: 'today', desc: '', args: []);
  }

  /// `Time`
  String get time {
    return Intl.message('Time', name: 'time', desc: '', args: []);
  }

  /// `Reset Password`
  String get resetPasswordButton {
    return Intl.message(
      'Reset Password',
      name: 'resetPasswordButton',
      desc: '',
      args: [],
    );
  }

  /// `Enter your phone number to send a verification code`
  String get forgotPasswordMessage {
    return Intl.message(
      'Enter your phone number to send a verification code',
      name: 'forgotPasswordMessage',
      desc: '',
      args: [],
    );
  }

  /// `Enter a new password`
  String get newPasswordMessage {
    return Intl.message(
      'Enter a new password',
      name: 'newPasswordMessage',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `Edit Profile`
  String get editProfile {
    return Intl.message(
      'Edit Profile',
      name: 'editProfile',
      desc: '',
      args: [],
    );
  }

  /// `Personal Info`
  String get personalInfo {
    return Intl.message(
      'Personal Info',
      name: 'personalInfo',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get notifications {
    return Intl.message(
      'Notifications',
      name: 'notifications',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacyPolicy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacyPolicy',
      desc: '',
      args: [],
    );
  }

  /// `About Us`
  String get aboutUs {
    return Intl.message('About Us', name: 'aboutUs', desc: '', args: []);
  }

  /// `Logout`
  String get logout {
    return Intl.message('Logout', name: 'logout', desc: '', args: []);
  }

  /// `Are you sure you want to log out?`
  String get logoutConfirmation {
    return Intl.message(
      'Are you sure you want to log out?',
      name: 'logoutConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Profile updated successfully`
  String get profileUpdatedSuccessfully {
    return Intl.message(
      'Profile updated successfully',
      name: 'profileUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Phone verification is required for update`
  String get phoneVerificationRequired {
    return Intl.message(
      'Phone verification is required for update',
      name: 'phoneVerificationRequired',
      desc: '',
      args: [],
    );
  }

  /// `New Phone Number`
  String get newPhone {
    return Intl.message(
      'New Phone Number',
      name: 'newPhone',
      desc: '',
      args: [],
    );
  }

  /// `Current Password`
  String get currentPassword {
    return Intl.message(
      'Current Password',
      name: 'currentPassword',
      desc: '',
      args: [],
    );
  }

  /// `New Password`
  String get newPassword {
    return Intl.message(
      'New Password',
      name: 'newPassword',
      desc: '',
      args: [],
    );
  }

  /// `Save Changes`
  String get saveChanges {
    return Intl.message(
      'Save Changes',
      name: 'saveChanges',
      desc: '',
      args: [],
    );
  }

  /// `An error occurred`
  String get errorOccurred {
    return Intl.message(
      'An error occurred',
      name: 'errorOccurred',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get retry {
    return Intl.message('Retry', name: 'retry', desc: '', args: []);
  }

  /// `Payment Method`
  String get paymentMethod {
    return Intl.message(
      'Payment Method',
      name: 'paymentMethod',
      desc: '',
      args: [],
    );
  }

  /// `Cash`
  String get cash {
    return Intl.message('Cash', name: 'cash', desc: '', args: []);
  }

  /// `Points`
  String get points {
    return Intl.message('Points', name: 'points', desc: '', args: []);
  }

  /// `No notifications`
  String get noNotifications {
    return Intl.message(
      'No notifications',
      name: 'noNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Unread Notifications`
  String get unreadNotifications {
    return Intl.message(
      'Unread Notifications',
      name: 'unreadNotifications',
      desc: '',
      args: [],
    );
  }

  /// `Exit`
  String get exit {
    return Intl.message('Exit', name: 'exit', desc: '', args: []);
  }

  /// `Exit App`
  String get exitApp {
    return Intl.message('Exit App', name: 'exitApp', desc: '', args: []);
  }

  /// `Are you sure you want to exit the app?`
  String get exitAppConfirmation {
    return Intl.message(
      'Are you sure you want to exit the app?',
      name: 'exitAppConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Products`
  String get products {
    return Intl.message('Products', name: 'products', desc: '', args: []);
  }

  /// `Categories`
  String get categories {
    return Intl.message('Categories', name: 'categories', desc: '', args: []);
  }

  /// `Scan Product`
  String get scanProduct {
    return Intl.message(
      'Scan Product',
      name: 'scanProduct',
      desc: '',
      args: [],
    );
  }

  /// `Scan QR Code`
  String get scanQRCode {
    return Intl.message('Scan QR Code', name: 'scanQRCode', desc: '', args: []);
  }

  /// `Scanned Products`
  String get scannedProducts {
    return Intl.message(
      'Scanned Products',
      name: 'scannedProducts',
      desc: '',
      args: [],
    );
  }

  /// `Product Details`
  String get productDetails {
    return Intl.message(
      'Product Details',
      name: 'productDetails',
      desc: '',
      args: [],
    );
  }

  /// `Usage Plan`
  String get usagePlan {
    return Intl.message('Usage Plan', name: 'usagePlan', desc: '', args: []);
  }

  /// `Create Plan`
  String get createPlan {
    return Intl.message('Create Plan', name: 'createPlan', desc: '', args: []);
  }

  /// `Edit Plan`
  String get editPlan {
    return Intl.message('Edit Plan', name: 'editPlan', desc: '', args: []);
  }

  /// `Generate QR`
  String get generateQR {
    return Intl.message('Generate QR', name: 'generateQR', desc: '', args: []);
  }

  /// `Save QR`
  String get saveQR {
    return Intl.message('Save QR', name: 'saveQR', desc: '', args: []);
  }

  /// `Share QR`
  String get shareQR {
    return Intl.message('Share QR', name: 'shareQR', desc: '', args: []);
  }

  /// `QR code saved successfully`
  String get qrSavedSuccessfully {
    return Intl.message(
      'QR code saved successfully',
      name: 'qrSavedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to save QR code`
  String get qrSaveFailed {
    return Intl.message(
      'Failed to save QR code',
      name: 'qrSaveFailed',
      desc: '',
      args: [],
    );
  }

  /// `Day`
  String get day {
    return Intl.message('Day', name: 'day', desc: '', args: []);
  }

  /// `Days`
  String get days {
    return Intl.message('Days', name: 'days', desc: '', args: []);
  }

  /// `Current Day`
  String get currentDay {
    return Intl.message('Current Day', name: 'currentDay', desc: '', args: []);
  }

  /// `Completed`
  String get completed {
    return Intl.message('Completed', name: 'completed', desc: '', args: []);
  }

  /// `Pending`
  String get pending {
    return Intl.message('Pending', name: 'pending', desc: '', args: []);
  }

  /// `Mark as Completed`
  String get markAsCompleted {
    return Intl.message(
      'Mark as Completed',
      name: 'markAsCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Progress`
  String get progress {
    return Intl.message('Progress', name: 'progress', desc: '', args: []);
  }

  /// `Buy Now`
  String get buyNow {
    return Intl.message('Buy Now', name: 'buyNow', desc: '', args: []);
  }

  /// `Buy`
  String get buy {
    return Intl.message('Buy', name: 'buy', desc: '', args: []);
  }

  /// `No plan available`
  String get noPlanAvailable {
    return Intl.message(
      'No plan available',
      name: 'noPlanAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Scan QR code to start`
  String get scanToStart {
    return Intl.message(
      'Scan QR code to start',
      name: 'scanToStart',
      desc: '',
      args: [],
    );
  }

  /// `Camera permission required`
  String get cameraPermissionRequired {
    return Intl.message(
      'Camera permission required',
      name: 'cameraPermissionRequired',
      desc: '',
      args: [],
    );
  }

  /// `Camera permission denied`
  String get cameraPermissionDenied {
    return Intl.message(
      'Camera permission denied',
      name: 'cameraPermissionDenied',
      desc: '',
      args: [],
    );
  }

  /// `Camera permission permanently denied. Please enable it from settings`
  String get cameraPermissionPermanentlyDenied {
    return Intl.message(
      'Camera permission permanently denied. Please enable it from settings',
      name: 'cameraPermissionPermanentlyDenied',
      desc: '',
      args: [],
    );
  }

  /// `Open Settings`
  String get openSettings {
    return Intl.message(
      'Open Settings',
      name: 'openSettings',
      desc: '',
      args: [],
    );
  }

  /// `Storage permission required`
  String get storagePermissionRequired {
    return Intl.message(
      'Storage permission required',
      name: 'storagePermissionRequired',
      desc: '',
      args: [],
    );
  }

  /// `Storage permission denied`
  String get storagePermissionDenied {
    return Intl.message(
      'Storage permission denied',
      name: 'storagePermissionDenied',
      desc: '',
      args: [],
    );
  }

  /// `Add Entry`
  String get addEntry {
    return Intl.message('Add Entry', name: 'addEntry', desc: '', args: []);
  }

  /// `Remove Entry`
  String get removeEntry {
    return Intl.message(
      'Remove Entry',
      name: 'removeEntry',
      desc: '',
      args: [],
    );
  }

  /// `Plan Entry`
  String get planEntry {
    return Intl.message('Plan Entry', name: 'planEntry', desc: '', args: []);
  }

  /// `Action`
  String get action {
    return Intl.message('Action', name: 'action', desc: '', args: []);
  }

  /// `Action Description`
  String get actionDescription {
    return Intl.message(
      'Action Description',
      name: 'actionDescription',
      desc: '',
      args: [],
    );
  }

  /// `Select Time`
  String get selectTime {
    return Intl.message('Select Time', name: 'selectTime', desc: '', args: []);
  }

  /// `Repeat Count`
  String get repeatCount {
    return Intl.message(
      'Repeat Count',
      name: 'repeatCount',
      desc: '',
      args: [],
    );
  }

  /// `Save Plan`
  String get savePlan {
    return Intl.message('Save Plan', name: 'savePlan', desc: '', args: []);
  }

  /// `Plan saved successfully`
  String get planSavedSuccessfully {
    return Intl.message(
      'Plan saved successfully',
      name: 'planSavedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to save plan`
  String get planSaveFailed {
    return Intl.message(
      'Failed to save plan',
      name: 'planSaveFailed',
      desc: '',
      args: [],
    );
  }

  /// `Delete Plan`
  String get deletePlan {
    return Intl.message('Delete Plan', name: 'deletePlan', desc: '', args: []);
  }

  /// `Are you sure you want to delete this plan?`
  String get deletePlanConfirmation {
    return Intl.message(
      'Are you sure you want to delete this plan?',
      name: 'deletePlanConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Plan deleted successfully`
  String get planDeletedSuccessfully {
    return Intl.message(
      'Plan deleted successfully',
      name: 'planDeletedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `No products found`
  String get noProductsFound {
    return Intl.message(
      'No products found',
      name: 'noProductsFound',
      desc: '',
      args: [],
    );
  }

  /// `Loading products...`
  String get loadingProducts {
    return Intl.message(
      'Loading products...',
      name: 'loadingProducts',
      desc: '',
      args: [],
    );
  }

  /// `Loading categories...`
  String get loadingCategories {
    return Intl.message(
      'Loading categories...',
      name: 'loadingCategories',
      desc: '',
      args: [],
    );
  }

  /// `Admin`
  String get admin {
    return Intl.message('Admin', name: 'admin', desc: '', args: []);
  }

  /// `User`
  String get user {
    return Intl.message('User', name: 'user', desc: '', args: []);
  }

  /// `Reminders`
  String get reminders {
    return Intl.message('Reminders', name: 'reminders', desc: '', args: []);
  }

  /// `Schedule Reminder`
  String get scheduleReminder {
    return Intl.message(
      'Schedule Reminder',
      name: 'scheduleReminder',
      desc: '',
      args: [],
    );
  }

  /// `Reminder scheduled`
  String get reminderScheduled {
    return Intl.message(
      'Reminder scheduled',
      name: 'reminderScheduled',
      desc: '',
      args: [],
    );
  }

  /// `Cancel Reminder`
  String get cancelReminder {
    return Intl.message(
      'Cancel Reminder',
      name: 'cancelReminder',
      desc: '',
      args: [],
    );
  }

  /// `Reschedule Reminder`
  String get rescheduleReminder {
    return Intl.message(
      'Reschedule Reminder',
      name: 'rescheduleReminder',
      desc: '',
      args: [],
    );
  }

  /// `Product Usage Reminder`
  String get notificationTitle {
    return Intl.message(
      'Product Usage Reminder',
      name: 'notificationTitle',
      desc: '',
      args: [],
    );
  }

  /// `View All`
  String get viewAll {
    return Intl.message('View All', name: 'viewAll', desc: '', args: []);
  }

  /// `Filter`
  String get filter {
    return Intl.message('Filter', name: 'filter', desc: '', args: []);
  }

  /// `Sort By`
  String get sortBy {
    return Intl.message('Sort By', name: 'sortBy', desc: '', args: []);
  }

  /// `Price`
  String get price {
    return Intl.message('Price', name: 'price', desc: '', args: []);
  }

  /// `Name`
  String get name {
    return Intl.message('Name', name: 'name', desc: '', args: []);
  }

  /// `Newest`
  String get newest {
    return Intl.message('Newest', name: 'newest', desc: '', args: []);
  }

  /// `Oldest`
  String get oldest {
    return Intl.message('Oldest', name: 'oldest', desc: '', args: []);
  }

  /// `Position the QR code within the frame`
  String get positionQRCode {
    return Intl.message(
      'Position the QR code within the frame',
      name: 'positionQRCode',
      desc: '',
      args: [],
    );
  }

  /// `Loading product...`
  String get loadingProduct {
    return Intl.message(
      'Loading product...',
      name: 'loadingProduct',
      desc: '',
      args: [],
    );
  }

  /// `Product not found`
  String get productNotFound {
    return Intl.message(
      'Product not found',
      name: 'productNotFound',
      desc: '',
      args: [],
    );
  }

  /// `Product scanned successfully!`
  String get productScannedSuccessfully {
    return Intl.message(
      'Product scanned successfully!',
      name: 'productScannedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Error: {message}`
  String errorMessage(Object message) {
    return Intl.message(
      'Error: $message',
      name: 'errorMessage',
      desc: '',
      args: [message],
    );
  }

  /// `Tap + to add entries`
  String get tapToAddEntries {
    return Intl.message(
      'Tap + to add entries',
      name: 'tapToAddEntries',
      desc: '',
      args: [],
    );
  }

  /// `Unknown Product`
  String get unknownProduct {
    return Intl.message(
      'Unknown Product',
      name: 'unknownProduct',
      desc: '',
      args: [],
    );
  }

  /// `Scan a product QR code to start tracking your usage plan`
  String get scanProductToStart {
    return Intl.message(
      'Scan a product QR code to start tracking your usage plan',
      name: 'scanProductToStart',
      desc: '',
      args: [],
    );
  }

  /// `Step`
  String get step {
    return Intl.message('Step', name: 'step', desc: '', args: []);
  }

  /// `Step {number}`
  String stepNumber(Object number) {
    return Intl.message(
      'Step $number',
      name: 'stepNumber',
      desc: '',
      args: [number],
    );
  }

  /// `Morning`
  String get morning {
    return Intl.message('Morning', name: 'morning', desc: '', args: []);
  }

  /// `Afternoon`
  String get afternoon {
    return Intl.message('Afternoon', name: 'afternoon', desc: '', args: []);
  }

  /// `Evening`
  String get evening {
    return Intl.message('Evening', name: 'evening', desc: '', args: []);
  }

  /// `Night`
  String get night {
    return Intl.message('Night', name: 'night', desc: '', args: []);
  }

  /// `times`
  String get times {
    return Intl.message('times', name: 'times', desc: '', args: []);
  }

  /// `Once`
  String get once {
    return Intl.message('Once', name: 'once', desc: '', args: []);
  }

  /// `Twice`
  String get twice {
    return Intl.message('Twice', name: 'twice', desc: '', args: []);
  }

  /// `{count} times`
  String timesCount(Object count) {
    return Intl.message(
      '$count times',
      name: 'timesCount',
      desc: '',
      args: [count],
    );
  }

  /// `No entries yet`
  String get noEntriesYet {
    return Intl.message(
      'No entries yet',
      name: 'noEntriesYet',
      desc: '',
      args: [],
    );
  }

  /// `Plan created successfully`
  String get planCreatedSuccessfully {
    return Intl.message(
      'Plan created successfully',
      name: 'planCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Plan updated successfully`
  String get planUpdatedSuccessfully {
    return Intl.message(
      'Plan updated successfully',
      name: 'planUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load product`
  String get failedToLoadProduct {
    return Intl.message(
      'Failed to load product',
      name: 'failedToLoadProduct',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load plan`
  String get failedToLoadPlan {
    return Intl.message(
      'Failed to load plan',
      name: 'failedToLoadPlan',
      desc: '',
      args: [],
    );
  }

  /// `Invalid QR code`
  String get invalidQRCode {
    return Intl.message(
      'Invalid QR code',
      name: 'invalidQRCode',
      desc: '',
      args: [],
    );
  }

  /// `QR code scanned`
  String get qrCodeScanned {
    return Intl.message(
      'QR code scanned',
      name: 'qrCodeScanned',
      desc: '',
      args: [],
    );
  }

  /// `Scanning QR code...`
  String get scanningQRCode {
    return Intl.message(
      'Scanning QR code...',
      name: 'scanningQRCode',
      desc: '',
      args: [],
    );
  }

  /// `Store`
  String get store {
    return Intl.message('Store', name: 'store', desc: '', args: []);
  }

  /// `My Products`
  String get myProducts {
    return Intl.message('My Products', name: 'myProducts', desc: '', args: []);
  }

  /// `No scanned products yet`
  String get noScannedProducts {
    return Intl.message(
      'No scanned products yet',
      name: 'noScannedProducts',
      desc: '',
      args: [],
    );
  }

  /// `Delete Product`
  String get deleteProduct {
    return Intl.message(
      'Delete Product',
      name: 'deleteProduct',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this product?`
  String get deleteProductConfirmation {
    return Intl.message(
      'Are you sure you want to delete this product?',
      name: 'deleteProductConfirmation',
      desc: '',
      args: [],
    );
  }

  /// `Product deleted successfully`
  String get productDeleted {
    return Intl.message(
      'Product deleted successfully',
      name: 'productDeleted',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get all {
    return Intl.message('All', name: 'all', desc: '', args: []);
  }

  /// `Add New Entry`
  String get addNewEntry {
    return Intl.message(
      'Add New Entry',
      name: 'addNewEntry',
      desc: '',
      args: [],
    );
  }

  /// `Edit Entry`
  String get editEntry {
    return Intl.message('Edit Entry', name: 'editEntry', desc: '', args: []);
  }

  /// `Enter action (e.g., Apply, Rinse)`
  String get enterAction {
    return Intl.message(
      'Enter action (e.g., Apply, Rinse)',
      name: 'enterAction',
      desc: '',
      args: [],
    );
  }

  /// `Enter description`
  String get enterDescription {
    return Intl.message(
      'Enter description',
      name: 'enterDescription',
      desc: '',
      args: [],
    );
  }

  /// `Enter additional notes`
  String get enterNotes {
    return Intl.message(
      'Enter additional notes',
      name: 'enterNotes',
      desc: '',
      args: [],
    );
  }

  /// `Please enter an action`
  String get pleaseEnterAction {
    return Intl.message(
      'Please enter an action',
      name: 'pleaseEnterAction',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
