import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luster/src/app.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:xr_helper/xr_helper.dart';

import 'firebase_options.dart';

// Supabase configuration
const supabaseUrl = 'https://wfrofrmeibpidjujrzrv.supabase.co';
const supabaseKey = String.fromEnvironment(
  'SUPABASE_KEY',
  defaultValue:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indmcm9mcm1laWJwaWRqdWpyenJ2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NjQyNTk4NzYsImV4cCI6MjA3OTgzNTg3Nn0.Q0cRVi2yxIypGhopI2V2ZcwfCjyh7gVRMewkv-ck-Wc',
);

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorageService.init();

  HttpOverrides.global = MyHttpOverrides();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize Supabase
  await Supabase.initialize(
    url: supabaseUrl,
    anonKey: supabaseKey,
  );

  NotificationService.init();

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

  runApp(const ProviderScope(child: BaseApp()));
}
