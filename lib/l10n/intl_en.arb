{"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "dontHaveAnAccount": "Don't have an account?", "haveAnAccount": "Already have an account?", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "mobileNumber": "Mobile Number", "skip": "<PERSON><PERSON>", "home": "Home", "fullName": "Full Name", "seeAll": "See All", "welcomeWithName": "Hello, {name}", "about": "About", "next": "Next", "startNow": "Start Now", "description": "Description", "address": "Address", "enter": "Enter", "save": "Save", "submit": "Submit", "pickImage": "Pick Image", "locationPickedSuccessfully": "Location selected successfully", "pickLocation": "Pick Location", "tapToSelectLocation": "Tap to select location", "changeLocation": "Change Location", "noDataFound": "No Data Found", "pleaseAddYourSocialMedia": "Please add your social media accounts", "pleaseAddYourLocation": "Please add your location", "storeLogo": "Store Logo", "storeBackground": "Store Background", "storeName": "Store Name", "search": "Search", "searchForProducts": "Search for Products", "createAccount": "Create Account", "idNumber": "ID Number", "termsAndConditions": "I confirm that I have read and agree to the terms and conditions of this app", "invalidIdNumber": "Invalid ID Number", "invalidPhoneNumber": "Invalid Phone Number", "passwordsDoNotMatch": "Passwords do not match", "pleaseAcceptTerms": "Please accept the terms and conditions", "error": "An Error Occurred", "termsAndConditionsText": "I confirm that I have read, understood, and agree to the", "termsAndConditionsLink": "Terms and Conditions", "termsAndConditionsEnd": "of using this app", "termsDialogTitle": "Terms and Conditions", "termsDialogContent": "These are the terms and conditions of the app. Please read them carefully before accepting. The full content will be added later.", "close": "Close", "phoneHint": "05xxxxxxxx", "loginTitle": "<PERSON><PERSON>", "rememberMe": "Remember Me", "forgotPasswordLink": "Forgot Password?", "dontHaveAccount": "Don't have an account?", "verificationTitle": "Phone Verification", "verificationMessage": "A verification code was sent to your phone number", "enterVerificationCode": "Enter the verification code", "verify": "Verify", "resendCode": "Resend Code", "loginSuccessful": "Login successful", "registrationSuccessful": "Registration successful", "dataLoadError": "Error loading data", "from": "From: ", "to": "To: ", "homePage": "Home Page", "shipmentsHistory": "Shipments History", "payment": "Payment", "notes": "Notes", "note": "Note", "editData": "Edit Data", "cancel": "Cancel", "confirm": "Confirm", "invalidPhoneFormat": "Invalid phone format", "tasks": "Tasks", "searchShipment": "Search Shipment", "searchShipmentTitle": "Search Shipment", "searchShipmentButton": "Search Shipment", "searchShipmentDetails": "Shipment Search Details", "unableToGetCurrentLocation": "Unable to get current location", "statusUpdatedSuccessfully": "Status updated successfully", "errorUpdatingStatus": "Error updating status", "hideDistance": "Hide Distance", "checkDistance": "Check Distance", "ok": "OK", "takePhoto": "Take Photo", "pickFromGallery": "Pick from Gallery", "confirmed": "Delivered to departure station", "yes": "Yes", "no": "No", "today": "Today", "time": "Time", "resetPasswordButton": "Reset Password", "forgotPasswordMessage": "Enter your phone number to send a verification code", "newPasswordMessage": "Enter a new password", "profile": "Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Info", "notifications": "Notifications", "privacyPolicy": "Privacy Policy", "aboutUs": "About Us", "logout": "Logout", "logoutConfirmation": "Are you sure you want to log out?", "profileUpdatedSuccessfully": "Profile updated successfully", "phoneVerificationRequired": "Phone verification is required for update", "newPhone": "New Phone Number", "currentPassword": "Current Password", "newPassword": "New Password", "saveChanges": "Save Changes", "errorOccurred": "An error occurred", "retry": "Retry", "paymentMethod": "Payment Method", "cash": "Cash", "points": "Points", "noNotifications": "No notifications", "unreadNotifications": "Unread Notifications", "exit": "Exit", "exitApp": "Exit App", "exitAppConfirmation": "Are you sure you want to exit the app?", "products": "Products", "categories": "Categories", "scanProduct": "Scan Product", "scanQRCode": "Scan QR Code", "scannedProducts": "Scanned Products", "productDetails": "Product Details", "usagePlan": "Usage Plan", "createPlan": "Create Plan", "editPlan": "Edit Plan", "generateQR": "Generate QR", "saveQR": "Save QR", "shareQR": "Share QR", "qrSavedSuccessfully": "QR code saved successfully", "qrSaveFailed": "Failed to save QR code", "day": "Day", "days": "Days", "currentDay": "Current Day", "completed": "Completed", "pending": "Pending", "markAsCompleted": "<PERSON> as Completed", "progress": "Progress", "buyNow": "Buy Now", "buy": "Buy", "noPlanAvailable": "No plan available", "scanToStart": "Scan QR code to start", "cameraPermissionRequired": "Camera permission required", "cameraPermissionDenied": "Camera permission denied", "cameraPermissionPermanentlyDenied": "Camera permission permanently denied. Please enable it from settings", "openSettings": "Open Settings", "storagePermissionRequired": "Storage permission required", "storagePermissionDenied": "Storage permission denied", "addEntry": "Add Entry", "removeEntry": "Remove Entry", "planEntry": "Plan Entry", "action": "Action", "actionDescription": "Action Description", "selectTime": "Select Time", "repeatCount": "Repeat Count", "savePlan": "Save Plan", "planSavedSuccessfully": "Plan saved successfully", "planSaveFailed": "Failed to save plan", "deletePlan": "Delete Plan", "deletePlanConfirmation": "Are you sure you want to delete this plan?", "planDeletedSuccessfully": "Plan deleted successfully", "noProductsFound": "No products found", "loadingProducts": "Loading products...", "loadingCategories": "Loading categories...", "admin": "Admin", "user": "User", "reminders": "Reminders", "scheduleReminder": "Schedule Reminder", "reminderScheduled": "Reminder scheduled", "cancelReminder": "<PERSON><PERSON>", "rescheduleReminder": "Reschedule <PERSON>", "notificationTitle": "Product Usage Reminder", "viewAll": "View All", "filter": "Filter", "sortBy": "Sort By", "price": "Price", "name": "Name", "newest": "Newest", "oldest": "Oldest", "positionQRCode": "Position the QR code within the frame", "loadingProduct": "Loading product...", "productNotFound": "Product not found", "productScannedSuccessfully": "Product scanned successfully!", "errorMessage": "Error: {message}", "tapToAddEntries": "Tap + to add entries", "unknownProduct": "Unknown Product", "scanProductToStart": "Scan a product QR code to start tracking your usage plan", "step": "Step", "stepNumber": "Step {number}", "morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "night": "Night", "times": "times", "once": "Once", "twice": "Twice", "timesCount": "{count} times", "noEntriesYet": "No entries yet", "planCreatedSuccessfully": "Plan created successfully", "planUpdatedSuccessfully": "Plan updated successfully", "failedToLoadProduct": "Failed to load product", "failedToLoadPlan": "Failed to load plan", "invalidQRCode": "Invalid QR code", "qrCodeScanned": "QR code scanned", "scanningQRCode": "Scanning QR code...", "store": "Store", "myProducts": "My Products", "noScannedProducts": "No scanned products yet", "deleteProduct": "Delete Product", "deleteProductConfirmation": "Are you sure you want to delete this product?", "productDeleted": "Product deleted successfully", "all": "All", "addNewEntry": "Add New Entry", "editEntry": "Edit Entry", "enterAction": "Enter action (e.g., Apply, Rinse)", "enterDescription": "Enter description", "enterNotes": "Enter additional notes", "pleaseEnterAction": "Please enter an action"}