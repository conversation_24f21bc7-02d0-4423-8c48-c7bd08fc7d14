part of xr_helper;

class Validations {
  //! التحقق من كلمة المرور
  static String? password(
    String? value, {
    String? emptyPasswordMessage = "لا يمكن أن تكون كلمة المرور فارغة",
    String? passwordLengthMessage = "يجب أن تكون كلمة المرور 8 أحرف على الأقل",
  }) {
    if (value == null || value.isEmpty) {
      return emptyPasswordMessage;
    } else if (value.length < 8) {
      return passwordLengthMessage;
    }
    return null;
  }

  //! التحقق من رقم الهاتف
  static String? phoneNumber(
    String? value, {
    String? emptyPhoneMessage = "لا يمكن أن يكون رقم الهاتف فارغًا",
    String? phoneLengthMessage = "رقم الهاتف غير صالح",
  }) {
    String pattern = r'(^(?:[0]9)?[0-9]{1,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyPhoneMessage;
    } else if (!regExp.hasMatch(value)) {
      return phoneLengthMessage;
    }
    return null;
  }

  //! التحقق من إدخال أرقام فقط
  static String? numbersOnly(
    String? value, {
    String? emptyMessage = "لا يمكن أن يكون الحقل فارغًا",
    String? invalidMessage = "يُسمح بالأرقام فقط",
  }) {
    String pattern = r'(^[0-9]*$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyMessage;
    } else if (!regExp.hasMatch(value)) {
      return invalidMessage;
    }
    return null;
  }

  //! التحقق من البريد الإلكتروني
  static String? email(
    String? value, {
    String? emptyEmailMessage = "لا يمكن أن يكون البريد الإلكتروني فارغًا",
    String? invalidEmailMessage = "البريد الإلكتروني غير صالح",
  }) {
    final RegExp urlExp = RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    if (value == null || value.isEmpty) {
      return emptyEmailMessage;
    } else if (!urlExp.hasMatch(value)) {
      return invalidEmailMessage;
    }
    return null;
  }

  //! التحقق من الحقول غير الفارغة
  static String? mustBeNotEmpty(
    String? value, {
    String? emptyMessage = "لا يمكن أن يكون الحقل فارغًا",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }
    return null;
  }

  //! التحقق من الهوية الفلسطينية
  static String? palestinianId(
    String? value, {
    String? emptyMessage = "لا يمكن أن يكون رقم الهوية فارغًا",
    String? invalidMessage = "رقم الهوية غير صالح",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value.length != 9 || !RegExp(r'^[0-9]+$').hasMatch(value)) {
      return invalidMessage;
    }

    int sum = 0;
    for (int i = 0; i < value.length; i++) {
      int digit = int.parse(value[i]);
      int incNum = digit * ((i % 2) + 1);
      sum += (incNum > 9) ? incNum - 9 : incNum;
    }

    if (sum % 10 != 0) {
      return invalidMessage;
    }

    return null;
  }

  //! التحقق من رقم الهاتف الفلسطيني
  static String? palestinianPhoneNumber(
    String? value, {
    String? emptyMessage = "لا يمكن أن يكون رقم الهاتف فارغًا",
    String? invalidMessage = "رقم الهاتف غير صالح",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    List<String> patterns = [
      r'^(00972|0|\+972)[5][0-9]{8}$',
      r'^(00970|0|\+970)[5][0-9]{8}$',
      r'^(05[0-9]|0[12346789])([0-9]{7})$',
      r'^(00972|0|\+972|0|)[2][0-9]{7}$',
    ];

    bool isValid = false;
    for (String pattern in patterns) {
      if (RegExp(pattern).hasMatch(value)) {
        isValid = true;
        break;
      }
    }

    if (!isValid) {
      return invalidMessage;
    }

    return null;
  }

  //! التحقق من تأكيد كلمة المرور
  static String? confirmPassword(
    String? value,
    String? originalPassword, {
    String? emptyMessage = "لا يمكن أن يكون تأكيد كلمة المرور فارغًا",
    String? mismatchMessage = "كلمتا المرور غير متطابقتين",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value != originalPassword) {
      return mismatchMessage;
    }

    return null;
  }
}
