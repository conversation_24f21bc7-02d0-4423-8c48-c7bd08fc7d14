part of xr_helper;

class Validations {
  //! Validate password
  static String? password(
    String? value, {
    String? emptyPasswordMessage = "Password cannot be empty",
    String? passwordLengthMessage =
        "Password must be at least 8 characters long",
  }) {
    if (value == null || value.isEmpty) {
      return emptyPasswordMessage;
    } else if (value.length < 8) {
      return passwordLengthMessage;
    }
    return null;
  }

  //! Validate phone number
  static String? phoneNumber(
    String? value, {
    String? emptyPhoneMessage = "Phone number cannot be empty",
    String? phoneLengthMessage = "Invalid phone number",
  }) {
    String pattern = r'(^(?:[0]9)?[0-9]{1,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyPhoneMessage;
    } else if (!regExp.hasMatch(value)) {
      return phoneLengthMessage;
    }
    return null;
  }

  //! Validate numbers only
  static String? numbersOnly(
    String? value, {
    String? emptyMessage = "Field cannot be empty",
    String? invalidMessage = "Invalid number",
  }) {
    String pattern = r'(^[0-9]*$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyMessage;
    } else if (!regExp.hasMatch(value)) {
      return invalidMessage;
    }
    return null;
  }

  //! Validate email
  static String? email(
    String? value, {
    String? emptyEmailMessage = "Email cannot be empty",
    String? invalidEmailMessage = "Invalid email address",
  }) {
    final RegExp urlExp = RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    if (value == null || value.isEmpty) {
      return emptyEmailMessage;
    } else if (!urlExp.hasMatch(value)) {
      return invalidEmailMessage;
    }
    return null;
  }

  //! Validate non-empty field
  static String? mustBeNotEmpty(String? value,
      {String? emptyMessage = "Field cannot be empty"}) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }
    return null;
  }

  //! Validate Palestinian ID
  static String? palestinianId(
    String? value, {
    String? emptyMessage = "ID number cannot be empty",
    String? invalidMessage = "Invalid ID number",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value.length != 9 || !RegExp(r'^[0-9]+$').hasMatch(value)) {
      return invalidMessage;
    }

    int sum = 0;
    for (int i = 0; i < value.length; i++) {
      int digit = int.parse(value[i]);
      int incNum = digit * ((i % 2) + 1);
      sum += (incNum > 9) ? incNum - 9 : incNum;
    }

    if (sum % 10 != 0) {
      return invalidMessage;
    }

    return null;
  }

  //! Validate Palestinian phone number
  static String? palestinianPhoneNumber(
    String? value, {
    String? emptyMessage = "Phone number cannot be empty",
    String? invalidMessage = "Invalid phone number",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    // Palestinian phone number patterns
    List<String> patterns = [
      r'^(00972|0|\+972)[5][0-9]{8}$', // Mobile with code 972
      r'^(00970|0|\+970)[5][0-9]{8}$', // Mobile with code 970
      r'^(05[0-9]|0[12346789])([0-9]{7})$', // Local
      r'^(00972|0|\+972|0|)[2][0-9]{7}$', // Landline
    ];

    bool isValid = false;
    for (String pattern in patterns) {
      if (RegExp(pattern).hasMatch(value)) {
        isValid = true;
        break;
      }
    }

    if (!isValid) {
      return invalidMessage;
    }

    return null;
  }

  //! Validate confirm password
  static String? confirmPassword(
    String? value,
    String? originalPassword, {
    String? emptyMessage = "Confirm password cannot be empty",
    String? mismatchMessage = "Passwords do not match",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value != originalPassword) {
      return mismatchMessage;
    }

    return null;
  }
}
