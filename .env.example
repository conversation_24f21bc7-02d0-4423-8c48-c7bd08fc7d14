# Supabase Configuration
# DO NOT commit the actual .env file with real credentials
# Copy this file to .env and fill in your actual values

# Supabase URL (DEV)
SUPABASE_URL=https://wfrofrmeibpidjujrzrv.supabase.co

# Supabase Anonymous Key (DEV)
# For production, use your production key
SUPABASE_KEY=your_supabase_anon_key_here

# Database Password (DEV)
# Only needed for direct database access
DB_PASSWORD=your_db_password_here

# Instructions:
# 1. Copy this file: cp .env.example .env
# 2. Replace the placeholder values with your actual credentials
# 3. Never commit the .env file to version control
# 4. For CI/CD, use environment variables or secrets management

